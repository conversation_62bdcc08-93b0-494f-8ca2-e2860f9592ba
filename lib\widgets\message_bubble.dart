import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../models/message.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final VoidCallback? onTap;

  const MessageBubble({
    super.key,
    required this.message,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isUser = message.isFromUser;
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 4.h),
        child: Row(
          mainAxisAlignment: isUser 
              ? MainAxisAlignment.end 
              : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!isUser) ...[
              CircleAvatar(
                radius: 16.w,
                backgroundColor: theme.colorScheme.primary,
                child: Icon(
                  Icons.smart_toy,
                  size: 18.w,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 8.w),
            ],
            
            Flexible(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
                decoration: BoxDecoration(
                  color: isUser 
                      ? theme.colorScheme.primary
                      : theme.colorScheme.surface,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(18.r),
                    topRight: Radius.circular(18.r),
                    bottomLeft: Radius.circular(isUser ? 18.r : 4.r),
                    bottomRight: Radius.circular(isUser ? 4.r : 18.r),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 消息内容
                    _buildMessageContent(context, isUser),
                    
                    // 时间戳和状态
                    SizedBox(height: 4.h),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          DateFormat('HH:mm').format(message.timestamp),
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: isUser 
                                ? Colors.white.withOpacity(0.7)
                                : Colors.grey[500],
                          ),
                        ),
                        if (isUser) ...[
                          SizedBox(width: 4.w),
                          _buildStatusIcon(context),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            if (isUser) ...[
              SizedBox(width: 8.w),
              CircleAvatar(
                radius: 16.w,
                backgroundColor: theme.colorScheme.secondary,
                child: Icon(
                  Icons.person,
                  size: 18.w,
                  color: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, bool isUser) {
    switch (message.type) {
      case MessageType.text:
        return Text(
          message.content,
          style: TextStyle(
            fontSize: 16.sp,
            color: isUser ? Colors.white : Colors.black87,
            height: 1.3,
          ),
        );
      
      case MessageType.audio:
        return _buildAudioMessage(context, isUser);
      
      case MessageType.image:
        return _buildImageMessage(context);
      
      case MessageType.video:
        return _buildVideoMessage(context);
    }
  }

  Widget _buildAudioMessage(BuildContext context, bool isUser) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.play_arrow,
          color: isUser ? Colors.white : Colors.grey[700],
          size: 24.w,
        ),
        SizedBox(width: 8.w),
        Text(
          message.content.isNotEmpty ? message.content : '[语音消息]',
          style: TextStyle(
            fontSize: 16.sp,
            color: isUser ? Colors.white : Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildImageMessage(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (message.imageUrl != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.network(
              message.imageUrl!,
              width: 200.w,
              height: 150.h,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 200.w,
                  height: 150.h,
                  color: Colors.grey[300],
                  child: Icon(
                    Icons.broken_image,
                    size: 40.w,
                    color: Colors.grey[600],
                  ),
                );
              },
            ),
          ),
        if (message.content.isNotEmpty) ...[
          SizedBox(height: 8.h),
          Text(
            message.content,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black87,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildVideoMessage(BuildContext context) {
    return Container(
      width: 200.w,
      height: 150.h,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            Icons.play_circle_fill,
            size: 50.w,
            color: Colors.white,
          ),
          Positioned(
            bottom: 8.h,
            left: 8.w,
            child: Text(
              '[视频消息]',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIcon(BuildContext context) {
    switch (message.status) {
      case MessageStatus.sending:
        return SizedBox(
          width: 12.w,
          height: 12.w,
          child: CircularProgressIndicator(
            strokeWidth: 1.5,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.white.withOpacity(0.7),
            ),
          ),
        );
      
      case MessageStatus.sent:
        return Icon(
          Icons.check,
          size: 12.w,
          color: Colors.white.withOpacity(0.7),
        );
      
      case MessageStatus.delivered:
        return Icon(
          Icons.done_all,
          size: 12.w,
          color: Colors.white.withOpacity(0.7),
        );
      
      case MessageStatus.read:
        return Icon(
          Icons.done_all,
          size: 12.w,
          color: Colors.blue[300],
        );
      
      case MessageStatus.failed:
        return Icon(
          Icons.error_outline,
          size: 12.w,
          color: Colors.red[300],
        );
    }
  }
}
