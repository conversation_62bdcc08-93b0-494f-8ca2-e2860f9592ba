#!/bin/bash

# 数字人女友应用部署脚本

set -e

echo "🚀 开始部署数字人女友应用..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    if ! command -v flutter &> /dev/null; then
        log_warn "Flutter 未安装，将跳过前端构建"
        SKIP_FLUTTER=true
    fi
    
    log_info "依赖检查完成"
}

# 构建Flutter Web应用
build_flutter_web() {
    if [ "$SKIP_FLUTTER" = true ]; then
        log_warn "跳过Flutter Web构建"
        return
    fi
    
    log_info "构建Flutter Web应用..."
    
    # 获取依赖
    flutter pub get
    
    # 构建Web版本
    flutter build web --release
    
    # 创建web目录并复制构建产物
    mkdir -p web
    cp -r build/web/* web/
    
    log_info "Flutter Web构建完成"
}

# 构建Android APK
build_android_apk() {
    if [ "$SKIP_FLUTTER" = true ]; then
        log_warn "跳过Android APK构建"
        return
    fi
    
    log_info "构建Android APK..."
    
    # 构建APK
    flutter build apk --release
    
    # 创建releases目录
    mkdir -p releases
    cp build/app/outputs/flutter-apk/app-release.apk releases/digital-girlfriend-$(date +%Y%m%d).apk
    
    log_info "Android APK构建完成"
}

# 准备环境配置
prepare_environment() {
    log_info "准备环境配置..."
    
    # 创建必要的目录
    mkdir -p backend/uploads/audio
    mkdir -p backend/uploads/images
    mkdir -p backend/logs
    mkdir -p nginx/ssl
    
    # 复制环境配置文件
    if [ ! -f backend/.env ]; then
        if [ -f backend/.env.example ]; then
            cp backend/.env.example backend/.env
            log_warn "已创建 backend/.env 文件，请根据需要修改配置"
        else
            log_error "backend/.env.example 文件不存在"
            exit 1
        fi
    fi
    
    log_info "环境配置准备完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 停止现有服务
    docker-compose down
    
    # 构建并启动服务
    docker-compose up --build -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端API
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_info "后端API健康检查通过"
    else
        log_error "后端API健康检查失败"
        return 1
    fi
    
    # 检查前端
    if curl -f http://localhost > /dev/null 2>&1; then
        log_info "前端服务健康检查通过"
    else
        log_warn "前端服务健康检查失败"
    fi
    
    log_info "健康检查完成"
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    echo ""
    echo "📱 应用访问地址:"
    echo "  前端Web应用: http://localhost"
    echo "  后端API文档: http://localhost:8000/docs"
    echo "  健康检查: http://localhost:8000/health"
    echo ""
    echo "📁 文件位置:"
    echo "  Android APK: releases/"
    echo "  日志文件: backend/logs/"
    echo "  上传文件: backend/uploads/"
    echo ""
    echo "🔧 管理命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo ""
}

# 清理函数
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "部署失败，正在清理..."
        docker-compose down
    fi
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-flutter)
                SKIP_FLUTTER=true
                shift
                ;;
            --production)
                PRODUCTION=true
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-flutter  跳过Flutter构建"
                echo "  --production    生产环境部署"
                echo "  --help          显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    prepare_environment
    build_flutter_web
    build_android_apk
    start_services
    health_check
    show_deployment_info
    
    log_info "部署成功完成！🎉"
}

# 运行主函数
main "$@"
