server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Flutter Web应用配置
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Flutter特定文件
    location /flutter_service_worker.js {
        add_header Cache-Control "no-cache";
        expires 0;
    }

    location /manifest.json {
        add_header Cache-Control "no-cache";
        expires 0;
    }

    # 错误页面
    error_page 404 /index.html;
}
