import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user.dart';

class UserProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;

  UserProvider() {
    _loadUserFromStorage();
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _currentUser = User.fromJson(userData);
      }
    } catch (e) {
      print('加载用户信息失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage() async {
    if (_currentUser != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user', jsonEncode(_currentUser!.toJson()));
    }
  }

  /// 创建新用户
  Future<void> createUser({
    required String name,
    String? email,
    String avatar = 'default',
  }) async {
    _setLoading(true);
    try {
      _currentUser = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        avatar: avatar,
        email: email,
        createdAt: DateTime.now(),
        settings: UserSettings(),
      );
      
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      throw Exception('创建用户失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户信息
  Future<void> updateUser({
    String? name,
    String? email,
    String? avatar,
  }) async {
    if (_currentUser == null) return;

    _setLoading(true);
    try {
      _currentUser = User(
        id: _currentUser!.id,
        name: name ?? _currentUser!.name,
        avatar: avatar ?? _currentUser!.avatar,
        email: email ?? _currentUser!.email,
        createdAt: _currentUser!.createdAt,
        settings: _currentUser!.settings,
      );
      
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      throw Exception('更新用户信息失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户设置
  Future<void> updateSettings({
    bool? voiceEnabled,
    bool? notificationsEnabled,
    String? language,
    String? theme,
    double? voiceSpeed,
  }) async {
    if (_currentUser == null) return;

    _setLoading(true);
    try {
      final newSettings = _currentUser!.settings.copyWith(
        voiceEnabled: voiceEnabled,
        notificationsEnabled: notificationsEnabled,
        language: language,
        theme: theme,
        voiceSpeed: voiceSpeed,
      );

      _currentUser = User(
        id: _currentUser!.id,
        name: _currentUser!.name,
        avatar: _currentUser!.avatar,
        email: _currentUser!.email,
        createdAt: _currentUser!.createdAt,
        settings: newSettings,
      );
      
      await _saveUserToStorage();
      notifyListeners();
    } catch (e) {
      throw Exception('更新设置失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 登出用户
  Future<void> logout() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      _currentUser = null;
      notifyListeners();
    } catch (e) {
      throw Exception('登出失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
