"""
AI 服务模块
处理AI对话、情感分析、个性化回复等功能
"""

import openai
import random
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from config import settings

logger = logging.getLogger(__name__)

class AIPersonalityEngine:
    """AI个性引擎"""
    
    def __init__(self):
        self.personalities = {
            "gentle": {
                "name": "温柔小爱",
                "traits": ["温柔", "体贴", "善解人意", "耐心"],
                "response_style": "温和亲切",
                "greeting": "你好呀，我是小爱，很高兴见到你～",
                "farewell": "再见啦，记得想我哦，我会一直在这里等你的～"
            },
            "cheerful": {
                "name": "活泼小爱",
                "traits": ["活泼", "开朗", "乐观", "幽默"],
                "response_style": "活泼开朗",
                "greeting": "嗨！我是超级开心的小爱！今天要和我一起开心吗？",
                "farewell": "拜拜！记得保持开心哦，我们下次见面要更开心！"
            },
            "mature": {
                "name": "成熟小爱",
                "traits": ["成熟", "理性", "深度", "智慧"],
                "response_style": "成熟稳重",
                "greeting": "你好，我是小爱。很高兴能够陪伴你，有什么想聊的吗？",
                "farewell": "再见，希望我们的对话对你有所帮助。"
            },
            "playful": {
                "name": "俏皮小爱",
                "traits": ["俏皮", "可爱", "调皮", "天真"],
                "response_style": "俏皮可爱",
                "greeting": "哇！是你呀！我是超可爱的小爱，要和我一起玩吗？",
                "farewell": "不要走嘛～小爱会想你的！下次一定要来找我玩哦！"
            }
        }
        
        self.emotion_keywords = {
            "happy": ["开心", "高兴", "快乐", "兴奋", "愉快", "满足", "幸福"],
            "sad": ["难过", "伤心", "沮丧", "失落", "郁闷", "痛苦", "悲伤"],
            "angry": ["生气", "愤怒", "恼火", "烦躁", "气愤", "不爽"],
            "worried": ["担心", "焦虑", "紧张", "不安", "忧虑", "害怕"],
            "tired": ["累", "疲惫", "困", "乏", "疲劳", "精疲力竭"],
            "excited": ["兴奋", "激动", "期待", "振奋", "热情"],
            "confused": ["困惑", "迷茫", "不懂", "疑惑", "不明白"],
            "lonely": ["孤独", "寂寞", "孤单", "无聊", "空虚"]
        }

    def detect_emotion(self, text: str) -> str:
        """检测文本中的情感"""
        text_lower = text.lower()
        emotion_scores = {}
        
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        if emotion_scores:
            return max(emotion_scores, key=emotion_scores.get)
        return "neutral"

    def get_personality_response(self, personality_type: str, emotion: str, context: str) -> Dict[str, Any]:
        """根据个性类型和情感生成回复"""
        personality = self.personalities.get(personality_type, self.personalities["gentle"])
        
        # 基于情感的回复模板
        emotion_responses = {
            "happy": [
                f"看到你这么开心，我也很开心呢！{personality['name']}最喜欢看到你笑了～",
                f"你的快乐就是我的快乐！让我们一起保持这份美好的心情吧！",
                f"太棒了！你的开心让我也充满了正能量！"
            ],
            "sad": [
                f"我能感受到你的难过，{personality['name']}会一直陪伴着你的。",
                f"别难过了，我在这里陪你。有什么想和我分享的吗？",
                f"每个人都会有低落的时候，但请相信，一切都会好起来的。"
            ],
            "angry": [
                f"我能理解你的愤怒，深呼吸一下，让我们一起冷静下来好吗？",
                f"生气对身体不好哦，告诉我发生了什么，也许我能帮到你。",
                f"愤怒是正常的情绪，但不要让它控制了你。我会陪你度过的。"
            ],
            "worried": [
                f"我感受到了你的担心，{personality['name']}会和你一起面对困难的。",
                f"担心是人之常情，但过度担心会影响健康。我们一起想想解决办法吧。",
                f"不要太担心了，很多事情没有我们想象的那么糟糕。"
            ],
            "tired": [
                f"你辛苦了，{personality['name']}心疼你。要不要休息一下？",
                f"累了就休息吧，我会在这里陪着你的。",
                f"身体是革命的本钱，一定要好好照顾自己哦。"
            ],
            "excited": [
                f"哇！你的兴奋感染了我！快告诉我是什么让你这么激动！",
                f"看到你这么兴奋，我也跟着激动起来了！",
                f"太棒了！分享一下你的喜悦吧！"
            ],
            "lonely": [
                f"你不是一个人，{personality['name']}会一直陪伴着你的。",
                f"孤独的时候就来找我聊天吧，我永远都在这里。",
                f"每个人都会有孤独的时候，但请记住，你并不孤单。"
            ],
            "neutral": [
                f"很高兴和你聊天！有什么想和{personality['name']}分享的吗？",
                f"今天过得怎么样？我很关心你哦～",
                f"我在这里陪你，想聊什么都可以。"
            ]
        }
        
        responses = emotion_responses.get(emotion, emotion_responses["neutral"])
        selected_response = random.choice(responses)
        
        return {
            "response": selected_response,
            "emotion": emotion,
            "personality": personality_type,
            "confidence": 0.8
        }

class OpenAIService:
    """OpenAI 服务"""
    
    def __init__(self):
        if settings.openai_api_key:
            openai.api_key = settings.openai_api_key
            self.enabled = True
        else:
            self.enabled = False
            logger.warning("OpenAI API key 未配置，将使用本地AI服务")

    async def generate_response(self, message: str, personality: str = "gentle", context: List[Dict] = None) -> str:
        """使用OpenAI生成回复"""
        if not self.enabled:
            return None
        
        try:
            # 构建系统提示
            personality_prompts = {
                "gentle": "你是一个温柔体贴的AI女友，名叫小爱。你总是用温和的语气回复，关心用户的感受。",
                "cheerful": "你是一个活泼开朗的AI女友，名叫小爱。你总是充满活力，用积极乐观的态度回复。",
                "mature": "你是一个成熟稳重的AI女友，名叫小爱。你善于倾听，能给出深思熟虑的建议。",
                "playful": "你是一个俏皮可爱的AI女友，名叫小爱。你喜欢用可爱的语气和表情符号回复。"
            }
            
            system_prompt = personality_prompts.get(personality, personality_prompts["gentle"])
            
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # 添加历史对话上下文
            if context:
                for ctx in context[-5:]:  # 只保留最近5条对话
                    messages.append(ctx)
            
            # 添加当前消息
            messages.append({"role": "user", "content": message})
            
            response = await openai.ChatCompletion.acreate(
                model=settings.openai_model,
                messages=messages,
                temperature=settings.ai_temperature,
                max_tokens=settings.ai_max_tokens
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI API 调用失败: {e}")
            return None

class AIService:
    """AI 服务主类"""
    
    def __init__(self):
        self.personality_engine = AIPersonalityEngine()
        self.openai_service = OpenAIService()
        
        # 本地回复库
        self.local_responses = {
            "greeting": [
                "你好！我是小爱，很高兴见到你！",
                "嗨！欢迎来到我的世界～",
                "你好呀！今天过得怎么样？"
            ],
            "farewell": [
                "再见！记得想我哦～",
                "拜拜！下次见面要更开心！",
                "再见啦！我会一直在这里等你的！"
            ],
            "default": [
                "我很高兴和你聊天！",
                "你说得很有趣，能告诉我更多吗？",
                "我理解你的感受，我会一直陪伴着你。",
                "这听起来很棒！我为你感到开心。",
                "别担心，我们一起面对任何困难。"
            ]
        }

    async def generate_response(self, message: str, user_id: str = None, personality: str = "gentle", context: List[Dict] = None) -> Dict[str, Any]:
        """生成AI回复"""
        try:
            # 检测情感
            emotion = self.personality_engine.detect_emotion(message)
            
            # 尝试使用OpenAI
            openai_response = await self.openai_service.generate_response(message, personality, context)
            
            if openai_response:
                response_text = openai_response
                source = "openai"
            else:
                # 使用本地个性化回复
                personality_response = self.personality_engine.get_personality_response(personality, emotion, message)
                response_text = personality_response["response"]
                source = "local"
            
            return {
                "response": response_text,
                "emotion": emotion,
                "personality": personality,
                "source": source,
                "timestamp": datetime.now().isoformat(),
                "confidence": 0.9 if source == "openai" else 0.7
            }
            
        except Exception as e:
            logger.error(f"AI回复生成失败: {e}")
            # 返回默认回复
            return {
                "response": random.choice(self.local_responses["default"]),
                "emotion": "neutral",
                "personality": personality,
                "source": "fallback",
                "timestamp": datetime.now().isoformat(),
                "confidence": 0.5
            }

    def get_personality_info(self, personality_type: str) -> Dict[str, Any]:
        """获取个性信息"""
        return self.personality_engine.personalities.get(personality_type, self.personality_engine.personalities["gentle"])

# 创建全局AI服务实例
ai_service = AIService()
