#!/usr/bin/env python3
"""
数字人女友后端服务启动脚本
"""

import uvicorn
import logging
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import settings
from database import startup_database, shutdown_database

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(settings.log_file, encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        logger.info("正在启动数字人女友后端服务...")
        logger.info(f"配置信息: {settings.app_name} v{settings.app_version}")
        logger.info(f"运行模式: {'开发模式' if settings.debug else '生产模式'}")
        logger.info(f"数据库: {settings.database_url.split('://')[0]}")
        
        # 初始化数据库
        startup_database()
        
        # 启动服务器
        uvicorn.run(
            "main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level=settings.log_level.lower(),
            access_log=True,
            use_colors=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        shutdown_database()
        logger.info("服务已关闭")

if __name__ == "__main__":
    main()
