import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../providers/chat_provider.dart';
import 'profile_screen.dart';
import 'chat_history_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<UserProvider>(
        builder: (context, userProvider, child) {
          final user = userProvider.currentUser;
          if (user == null) {
            return const Center(
              child: Text('用户信息加载中...'),
            );
          }

          return ListView(
            padding: EdgeInsets.all(16.w),
            children: [
              // 用户信息卡片
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 40.w,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        child: Icon(
                          Icons.person,
                          size: 40.w,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        user.name,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      if (user.email != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          user.email!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                      SizedBox(height: 16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () => _showEditProfileDialog(context, userProvider),
                            child: const Text('编辑资料'),
                          ),
                          OutlinedButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ProfileScreen(),
                                ),
                              );
                            },
                            child: const Text('详细资料'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 16.h),

              // 应用设置
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Text(
                        '应用设置',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    SwitchListTile(
                      title: const Text('语音功能'),
                      subtitle: const Text('启用语音消息和语音回复'),
                      value: user.settings.voiceEnabled,
                      onChanged: (value) {
                        userProvider.updateSettings(voiceEnabled: value);
                      },
                    ),
                    SwitchListTile(
                      title: const Text('通知'),
                      subtitle: const Text('接收消息通知'),
                      value: user.settings.notificationsEnabled,
                      onChanged: (value) {
                        userProvider.updateSettings(notificationsEnabled: value);
                      },
                    ),
                    ListTile(
                      title: const Text('主题'),
                      subtitle: Text(_getThemeText(user.settings.theme)),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () => _showThemeDialog(context, userProvider),
                    ),
                    ListTile(
                      title: const Text('语言'),
                      subtitle: Text(_getLanguageText(user.settings.language)),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () => _showLanguageDialog(context, userProvider),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),

              // 聊天设置
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Text(
                        '聊天设置',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    ListTile(
                      title: const Text('聊天记录'),
                      subtitle: const Text('查看和管理聊天记录'),
                      trailing: const Icon(Icons.history),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ChatHistoryScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      title: const Text('清空聊天记录'),
                      subtitle: const Text('删除所有聊天消息'),
                      trailing: const Icon(Icons.delete_outline),
                      onTap: () => _showClearChatDialog(context),
                    ),
                    ListTile(
                      title: const Text('语音速度'),
                      subtitle: Text('${user.settings.voiceSpeed}x'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () => _showVoiceSpeedDialog(context, userProvider),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),

              // 其他选项
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Text(
                        '其他',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    ListTile(
                      title: const Text('关于应用'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () => _showAboutDialog(context),
                    ),
                    ListTile(
                      title: const Text('隐私政策'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        // TODO: 显示隐私政策
                      },
                    ),
                    ListTile(
                      title: const Text('用户协议'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        // TODO: 显示用户协议
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(height: 32.h),

              // 退出登录按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _showLogoutDialog(context, userProvider),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('退出登录'),
                ),
              ),
              SizedBox(height: 32.h),
            ],
          );
        },
      ),
    );
  }

  String _getThemeText(String theme) {
    switch (theme) {
      case 'light':
        return '浅色';
      case 'dark':
        return '深色';
      case 'system':
      default:
        return '跟随系统';
    }
  }

  String _getLanguageText(String language) {
    switch (language) {
      case 'zh-CN':
        return '简体中文';
      case 'en-US':
        return 'English';
      default:
        return '简体中文';
    }
  }

  void _showEditProfileDialog(BuildContext context, UserProvider userProvider) {
    final nameController = TextEditingController(text: userProvider.currentUser?.name);
    final emailController = TextEditingController(text: userProvider.currentUser?.email ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑资料'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '昵称',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: '邮箱',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await userProvider.updateUser(
                  name: nameController.text.trim(),
                  email: emailController.text.trim().isEmpty 
                      ? null 
                      : emailController.text.trim(),
                );
                Navigator.pop(context);
                Get.snackbar('成功', '资料更新成功');
              } catch (e) {
                Get.snackbar('错误', '更新失败: $e');
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('跟随系统'),
              value: 'system',
              groupValue: userProvider.currentUser?.settings.theme,
              onChanged: (value) {
                userProvider.updateSettings(theme: value);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('浅色'),
              value: 'light',
              groupValue: userProvider.currentUser?.settings.theme,
              onChanged: (value) {
                userProvider.updateSettings(theme: value);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('深色'),
              value: 'dark',
              groupValue: userProvider.currentUser?.settings.theme,
              onChanged: (value) {
                userProvider.updateSettings(theme: value);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择语言'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('简体中文'),
              value: 'zh-CN',
              groupValue: userProvider.currentUser?.settings.language,
              onChanged: (value) {
                userProvider.updateSettings(language: value);
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en-US',
              groupValue: userProvider.currentUser?.settings.language,
              onChanged: (value) {
                userProvider.updateSettings(language: value);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVoiceSpeedDialog(BuildContext context, UserProvider userProvider) {
    double currentSpeed = userProvider.currentUser?.settings.voiceSpeed ?? 1.0;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('语音速度'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('${currentSpeed.toStringAsFixed(1)}x'),
              Slider(
                value: currentSpeed,
                min: 0.5,
                max: 2.0,
                divisions: 15,
                onChanged: (value) {
                  setState(() {
                    currentSpeed = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                userProvider.updateSettings(voiceSpeed: currentSpeed);
                Navigator.pop(context);
              },
              child: const Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空聊天记录'),
        content: const Text('确定要清空所有聊天记录吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final chatProvider = Provider.of<ChatProvider>(
                context,
                listen: false,
              );
              chatProvider.clearMessages();
              Navigator.pop(context);
              Get.snackbar('成功', '聊天记录已清空');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于数字人女友'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('版本: 1.0.0'),
            SizedBox(height: 8.h),
            const Text('一个基于Flutter开发的AI数字人女友应用'),
            SizedBox(height: 8.h),
            const Text('支持文本和语音交互，提供温暖的AI陪伴体验。'),
            SizedBox(height: 16.h),
            const Text('开发者: 数字人女友团队'),
            SizedBox(height: 8.h),
            const Text('技术栈: Flutter + Python FastAPI'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await userProvider.logout();
                Navigator.pop(context);
                Get.offAllNamed('/');
              } catch (e) {
                Get.snackbar('错误', '退出登录失败: $e');
              }
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
