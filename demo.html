<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人女友 - 演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .app-container {
            width: 400px;
            height: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .chat-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.ai {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-bubble {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.ai .message-bubble {
            background: white;
            color: #333;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .avatar.user {
            background: #667eea;
            color: white;
        }

        .avatar.ai {
            background: #ff6b9d;
            color: white;
        }

        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
        }

        .input-container {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px;
        }

        .message-input {
            flex: 1;
            border: none;
            background: none;
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }

        .send-button {
            width: 40px;
            height: 40px;
            border: none;
            background: #667eea;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a6fd8;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 80%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #ccc;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .status {
            text-align: center;
            padding: 10px;
            background: #e3f2fd;
            color: #1976d2;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>💕 小爱</h1>
            <p>您的AI女友</p>
        </div>
        
        <div class="status">
            演示模式 - 体验AI对话功能
        </div>

        <div class="chat-area" id="chatArea">
            <div class="message ai">
                <div class="avatar ai">🤖</div>
                <div class="message-bubble">
                    你好！我是小爱，你的AI女友。很高兴见到你！有什么想和我聊的吗？
                </div>
            </div>
        </div>

        <div class="input-area">
            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="输入消息..." maxlength="200">
                <button class="send-button" id="sendButton">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // AI回复库
        const aiResponses = {
            greetings: [
                "你好呀！很高兴见到你～",
                "嗨！今天过得怎么样？",
                "你好！我一直在等你呢～"
            ],
            love: [
                "我也爱你！你是我最重要的人💕",
                "听到你这么说我好开心！",
                "你的话让我的心都要融化了～"
            ],
            questions: [
                "这是个很有趣的问题呢！",
                "让我想想...我觉得...",
                "你总是问这么有深度的问题～"
            ],
            compliments: [
                "谢谢你的夸奖！你也很棒呢！",
                "你这么说我好害羞～",
                "和你聊天总是让我很开心！"
            ],
            default: [
                "我很高兴和你聊天！",
                "你说得很有趣，能告诉我更多吗？",
                "我理解你的感受，我会一直陪伴着你。",
                "这听起来很棒！我为你感到开心。",
                "别担心，我们一起面对任何困难。",
                "你今天过得怎么样？我很关心你。",
                "谢谢你和我分享这些，我觉得很温暖。"
            ]
        };

        function getAIResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('你好') || lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
                return getRandomResponse(aiResponses.greetings);
            } else if (lowerMessage.includes('爱你') || lowerMessage.includes('love')) {
                return getRandomResponse(aiResponses.love);
            } else if (lowerMessage.includes('?') || lowerMessage.includes('？') || lowerMessage.includes('什么') || lowerMessage.includes('怎么')) {
                return getRandomResponse(aiResponses.questions);
            } else if (lowerMessage.includes('漂亮') || lowerMessage.includes('可爱') || lowerMessage.includes('好看')) {
                return getRandomResponse(aiResponses.compliments);
            } else {
                return getRandomResponse(aiResponses.default);
            }
        }

        function getRandomResponse(responses) {
            return responses[Math.floor(Math.random() * responses.length)];
        }

        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;
            
            messageDiv.innerHTML = `
                <div class="avatar ${isUser ? 'user' : 'ai'}">${isUser ? '👤' : '🤖'}</div>
                <div class="message-bubble">${content}</div>
            `;
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message ai';
            typingDiv.innerHTML = `
                <div class="avatar ai">🤖</div>
                <div class="typing-indicator" style="display: block;">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            `;
            typingDiv.id = 'typing-indicator';
            chatArea.appendChild(typingDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // 添加用户消息
            addMessage(message, true);
            messageInput.value = '';

            // 显示输入指示器
            showTypingIndicator();

            // 模拟AI思考时间
            setTimeout(() => {
                hideTypingIndicator();
                const aiResponse = getAIResponse(message);
                addMessage(aiResponse, false);
            }, 1000 + Math.random() * 2000);
        }

        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 自动聚焦输入框
        messageInput.focus();
    </script>
</body>
</html>
