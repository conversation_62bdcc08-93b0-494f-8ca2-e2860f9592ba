#!/usr/bin/env python3
"""
数字人女友应用集成测试脚本
测试前后端功能是否正常工作
"""

import asyncio
import aiohttp
import websockets
import json
import time
import sys
from pathlib import Path

# 配置
BACKEND_URL = "http://localhost:8000"
WEBSOCKET_URL = "ws://localhost:8000/ws"

class IntegrationTester:
    def __init__(self):
        self.session = None
        self.test_results = []

    async def setup(self):
        """初始化测试环境"""
        self.session = aiohttp.ClientSession()
        print("🔧 测试环境初始化完成")

    async def cleanup(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
        print("🧹 测试环境清理完成")

    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "name": test_name,
            "success": success,
            "message": message
        })

    async def test_backend_health(self):
        """测试后端健康检查"""
        try:
            async with self.session.get(f"{BACKEND_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_result("后端健康检查", True, f"状态: {data.get('status')}")
                else:
                    self.log_result("后端健康检查", False, f"HTTP {response.status}")
        except Exception as e:
            self.log_result("后端健康检查", False, str(e))

    async def test_chat_api(self):
        """测试聊天API"""
        try:
            payload = {
                "message": "你好，我是测试用户",
                "timestamp": "2024-01-01T00:00:00Z",
                "user_id": "test_user"
            }
            
            async with self.session.post(
                f"{BACKEND_URL}/api/chat/text",
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if "response" in data:
                        self.log_result("聊天API", True, f"回复: {data['response'][:50]}...")
                    else:
                        self.log_result("聊天API", False, "响应格式错误")
                else:
                    self.log_result("聊天API", False, f"HTTP {response.status}")
        except Exception as e:
            self.log_result("聊天API", False, str(e))

    async def test_websocket_connection(self):
        """测试WebSocket连接"""
        try:
            async with websockets.connect(WEBSOCKET_URL) as websocket:
                # 发送测试消息
                test_message = {
                    "id": "test_msg_001",
                    "content": "WebSocket测试消息",
                    "type": "text",
                    "timestamp": "2024-01-01T00:00:00Z",
                    "isFromUser": True
                }
                
                await websocket.send(json.dumps(test_message))
                
                # 等待回复
                response = await asyncio.wait_for(websocket.recv(), timeout=10)
                response_data = json.loads(response)
                
                if "content" in response_data:
                    self.log_result("WebSocket通信", True, f"收到回复: {response_data['content'][:50]}...")
                else:
                    self.log_result("WebSocket通信", False, "响应格式错误")
                    
        except asyncio.TimeoutError:
            self.log_result("WebSocket通信", False, "响应超时")
        except Exception as e:
            self.log_result("WebSocket通信", False, str(e))

    async def test_emotion_api(self):
        """测试情感状态API"""
        try:
            async with self.session.get(f"{BACKEND_URL}/api/emotion/state") as response:
                if response.status == 200:
                    data = await response.json()
                    if "emotion" in data:
                        self.log_result("情感状态API", True, f"情感: {data['emotion']}")
                    else:
                        self.log_result("情感状态API", False, "响应格式错误")
                else:
                    self.log_result("情感状态API", False, f"HTTP {response.status}")
        except Exception as e:
            self.log_result("情感状态API", False, str(e))

    async def test_personality_api(self):
        """测试个性设置API"""
        try:
            payload = {
                "personality_type": "gentle",
                "response_style": "caring",
                "interests": ["音乐", "电影", "读书"]
            }
            
            async with self.session.post(
                f"{BACKEND_URL}/api/personality/update",
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        self.log_result("个性设置API", True, "设置更新成功")
                    else:
                        self.log_result("个性设置API", False, "更新失败")
                else:
                    self.log_result("个性设置API", False, f"HTTP {response.status}")
        except Exception as e:
            self.log_result("个性设置API", False, str(e))

    async def test_cors_headers(self):
        """测试CORS配置"""
        try:
            headers = {"Origin": "http://localhost:3000"}
            async with self.session.options(
                f"{BACKEND_URL}/api/chat/text",
                headers=headers
            ) as response:
                cors_headers = response.headers.get("Access-Control-Allow-Origin")
                if cors_headers:
                    self.log_result("CORS配置", True, f"允许源: {cors_headers}")
                else:
                    self.log_result("CORS配置", False, "CORS头缺失")
        except Exception as e:
            self.log_result("CORS配置", False, str(e))

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始集成测试...")
        print("=" * 50)
        
        await self.setup()
        
        # 运行各项测试
        await self.test_backend_health()
        await self.test_chat_api()
        await self.test_websocket_connection()
        await self.test_emotion_api()
        await self.test_personality_api()
        await self.test_cors_headers()
        
        await self.cleanup()
        
        # 输出测试结果
        print("=" * 50)
        print("📊 测试结果汇总:")
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("⚠️  部分测试失败，请检查后端服务")
            return False

def check_backend_running():
    """检查后端服务是否运行"""
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8000))
        sock.close()
        return result == 0
    except:
        return False

async def main():
    """主函数"""
    print("🔍 数字人女友应用集成测试")
    print("=" * 50)
    
    # 检查后端服务
    if not check_backend_running():
        print("❌ 后端服务未运行！")
        print("请先启动后端服务:")
        print("  cd backend")
        print("  python run_server.py")
        sys.exit(1)
    
    print("✅ 后端服务正在运行")
    print()
    
    # 运行测试
    tester = IntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 集成测试完成，系统运行正常！")
        print("\n下一步:")
        print("1. 启动Flutter应用: flutter run")
        print("2. 在应用中测试聊天功能")
        print("3. 检查WebSocket实时通信")
        sys.exit(0)
    else:
        print("\n🔧 请修复失败的测试项后重新运行")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        sys.exit(1)
