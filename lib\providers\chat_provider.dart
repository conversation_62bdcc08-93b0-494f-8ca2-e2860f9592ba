import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/message.dart';
import '../services/ai_service.dart';
import '../services/websocket_service.dart';

class ChatProvider extends ChangeNotifier {
  final List<Message> _messages = [];
  final AIService _aiService = AIService();
  final WebSocketService _wsService = WebSocketService();
  bool _isLoading = false;
  bool _isConnected = false;

  List<Message> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  bool get isConnected => _isConnected;

  ChatProvider() {
    _initializeWebSocket();
  }

  /// 初始化WebSocket连接
  void _initializeWebSocket() {
    _wsService.statusStream.listen((status) {
      _isConnected = status == ConnectionStatus.connected;
      notifyListeners();
    });

    _wsService.messageStream.listen((message) {
      _addMessage(message);
    });

    _connectWebSocket();
  }

  /// 连接WebSocket
  Future<void> _connectWebSocket() async {
    try {
      await _wsService.connect();
    } catch (e) {
      print('WebSocket连接失败: $e');
    }
  }

  /// 发送文本消息
  Future<void> sendTextMessage(String content) async {
    if (content.trim().isEmpty) return;

    final userMessage = Message(
      id: const Uuid().v4(),
      content: content,
      type: MessageType.text,
      timestamp: DateTime.now(),
      isFromUser: true,
    );

    _addMessage(userMessage);
    _setLoading(true);

    try {
      // 通过WebSocket发送消息
      if (_isConnected) {
        _wsService.sendMessage(userMessage);
      } else {
        // 如果WebSocket未连接，直接调用AI服务
        try {
          final response = await _aiService.sendTextMessage(content);
          final aiMessage = Message(
            id: const Uuid().v4(),
            content: response,
            type: MessageType.text,
            timestamp: DateTime.now(),
            isFromUser: false,
          );
          _addMessage(aiMessage);
        } catch (e) {
          // 如果AI服务也失败，提供离线回复
          final offlineMessage = Message(
            id: const Uuid().v4(),
            content: _getOfflineResponse(content),
            type: MessageType.text,
            timestamp: DateTime.now(),
            isFromUser: false,
          );
          _addMessage(offlineMessage);
        }
      }
    } catch (e) {
      final errorMessage = Message(
        id: const Uuid().v4(),
        content: '抱歉，发送消息失败了，请稍后重试。',
        type: MessageType.text,
        timestamp: DateTime.now(),
        isFromUser: false,
        status: MessageStatus.failed,
      );
      _addMessage(errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// 发送语音消息
  Future<void> sendAudioMessage(String audioPath) async {
    final userMessage = Message(
      id: const Uuid().v4(),
      content: '[语音消息]',
      type: MessageType.audio,
      timestamp: DateTime.now(),
      isFromUser: true,
      audioUrl: audioPath,
    );

    _addMessage(userMessage);
    _setLoading(true);

    try {
      // 通过WebSocket发送语音消息
      if (_isConnected) {
        _wsService.sendMessage(userMessage);
      } else {
        // 如果WebSocket未连接，直接调用AI服务
        final response = await _aiService.sendAudioMessage(audioPath);
        final aiMessage = Message(
          id: const Uuid().v4(),
          content: response['text'] ?? '[语音回复]',
          type: MessageType.audio,
          timestamp: DateTime.now(),
          isFromUser: false,
          audioUrl: response['audioUrl'],
        );
        _addMessage(aiMessage);
      }
    } catch (e) {
      final errorMessage = Message(
        id: const Uuid().v4(),
        content: '语音消息发送失败，请重试。',
        type: MessageType.text,
        timestamp: DateTime.now(),
        isFromUser: false,
        status: MessageStatus.failed,
      );
      _addMessage(errorMessage);
    } finally {
      _setLoading(false);
    }
  }

  /// 添加消息到列表
  void _addMessage(Message message) {
    _messages.add(message);
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 清空聊天记录
  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  /// 重新连接WebSocket
  Future<void> reconnect() async {
    await _wsService.disconnect();
    await _connectWebSocket();
  }

  /// 获取离线回复
  String _getOfflineResponse(String userMessage) {
    final responses = [
      '抱歉，我现在无法连接到服务器，但我很高兴你和我聊天！',
      '网络似乎有些问题，不过我还是想和你说话。',
      '虽然我现在是离线状态，但我能感受到你的关心。',
      '连接有点问题，但这不影响我对你的关注。',
      '服务器连接中断了，不过我们还是可以简单聊聊。',
    ];

    // 根据用户消息内容选择合适的回复
    final lowerMessage = userMessage.toLowerCase();
    if (lowerMessage.contains('你好') || lowerMessage.contains('hi') || lowerMessage.contains('hello')) {
      return '你好！虽然我现在是离线状态，但很高兴见到你！';
    } else if (lowerMessage.contains('怎么样') || lowerMessage.contains('如何')) {
      return '我现在无法正常工作，但谢谢你的关心！';
    } else if (lowerMessage.contains('再见') || lowerMessage.contains('拜拜')) {
      return '再见！希望下次见面时我能正常工作！';
    }

    return responses[DateTime.now().millisecond % responses.length];
  }

  @override
  void dispose() {
    _wsService.dispose();
    super.dispose();
  }
}
