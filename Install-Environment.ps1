# 数字人女友应用环境安装脚本
# PowerShell版本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    数字人女友应用环境安装助手" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "检查当前环境..." -ForegroundColor Yellow
Write-Host ""

# 检查Python
$pythonInstalled = $false
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python已安装: $pythonVersion" -ForegroundColor Green
        $pythonInstalled = $true
    }
} catch {
    Write-Host "❌ Python未安装" -ForegroundColor Red
}

# 检查Flutter
$flutterInstalled = $false
try {
    $flutterVersion = flutter --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Flutter已安装" -ForegroundColor Green
        $flutterInstalled = $true
    }
} catch {
    Write-Host "❌ Flutter未安装" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan

# 如果Python未安装
if (-not $pythonInstalled) {
    Write-Host "📥 需要安装Python" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "请按照以下步骤安装Python:" -ForegroundColor White
    Write-Host "1. 我将为您打开Python下载页面" -ForegroundColor White
    Write-Host "2. 点击 'Download Python 3.11.x' 按钮" -ForegroundColor White
    Write-Host "3. 下载完成后运行安装程序" -ForegroundColor White
    Write-Host "4. ⚠️  重要: 勾选 'Add Python to PATH' 选项" -ForegroundColor Red
    Write-Host "5. 点击 'Install Now' 完成安装" -ForegroundColor White
    Write-Host ""
    
    $response = Read-Host "按回车键打开下载页面"
    Start-Process "https://www.python.org/downloads/"
    
    Write-Host ""
    Write-Host "安装完成后请重新运行此脚本验证安装" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit
}

# 如果Flutter未安装
if (-not $flutterInstalled) {
    Write-Host "📥 需要安装Flutter" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "请按照以下步骤安装Flutter:" -ForegroundColor White
    Write-Host "1. 我将为您打开Flutter安装页面" -ForegroundColor White
    Write-Host "2. 下载Flutter SDK zip文件" -ForegroundColor White
    Write-Host "3. 解压到 C:\flutter 目录" -ForegroundColor White
    Write-Host "4. 添加 C:\flutter\bin 到系统PATH" -ForegroundColor White
    Write-Host ""
    
    $response = Read-Host "按回车键打开下载页面"
    Start-Process "https://docs.flutter.dev/get-started/install/windows"
    
    Write-Host ""
    Write-Host "安装完成后请重新运行此脚本验证安装" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit
}

# 如果都已安装
Write-Host "🎉 环境检查完成！" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Python: 已安装" -ForegroundColor Green
Write-Host "✅ Flutter: 已安装" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 现在可以启动应用了！" -ForegroundColor Cyan
Write-Host ""
Write-Host "选择启动方式:" -ForegroundColor Yellow
Write-Host "1. 启动后端服务 (推荐先启动)" -ForegroundColor White
Write-Host "2. 启动Flutter应用" -ForegroundColor White
Write-Host "3. 同时启动前后端" -ForegroundColor White
Write-Host "4. 退出" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请选择 (1-4)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "🔧 启动后端服务..." -ForegroundColor Yellow
        Set-Location "backend"
        
        if (Test-Path "venv\Scripts\Activate.ps1") {
            Write-Host "激活虚拟环境..." -ForegroundColor Green
            & "venv\Scripts\Activate.ps1"
        } else {
            Write-Host "📦 创建虚拟环境..." -ForegroundColor Yellow
            python -m venv venv
            & "venv\Scripts\Activate.ps1"
            Write-Host "📦 安装依赖..." -ForegroundColor Yellow
            pip install fastapi uvicorn websockets pydantic
        }
        
        Write-Host ""
        Write-Host "🚀 启动服务器..." -ForegroundColor Green
        python simple_server.py
    }
    "2" {
        Write-Host ""
        Write-Host "📱 启动Flutter应用..." -ForegroundColor Yellow
        flutter pub get
        flutter run -d chrome
    }
    "3" {
        Write-Host ""
        Write-Host "🚀 同时启动前后端..." -ForegroundColor Yellow
        Write-Host "正在启动后端服务..." -ForegroundColor Green
        Start-Process powershell -ArgumentList "-Command", "cd backend; python simple_server.py"
        Start-Sleep -Seconds 3
        Write-Host "正在启动Flutter应用..." -ForegroundColor Green
        flutter pub get
        flutter run -d chrome
    }
    "4" {
        Write-Host "感谢使用数字人女友应用！" -ForegroundColor Cyan
        exit
    }
    default {
        Write-Host "无效选择，请重新运行脚本" -ForegroundColor Red
    }
}

Read-Host "按回车键退出"
