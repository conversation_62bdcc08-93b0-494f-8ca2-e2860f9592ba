# Python 安装指南

## 🐍 快速安装Python

### 方法1: 从官网下载（推荐）

1. **访问Python官网**
   - 打开浏览器，访问: https://www.python.org/downloads/
   - 点击 "Download Python 3.11.x" 按钮

2. **下载安装包**
   - 下载适合Windows的安装包（通常是 .exe 文件）
   - 文件大小约25-30MB

3. **安装Python**
   - 双击下载的安装包
   - **重要**: 勾选 "Add Python to PATH" 选项
   - 点击 "Install Now"
   - 等待安装完成

4. **验证安装**
   - 打开命令提示符（按Win+R，输入cmd）
   - 输入: `python --version`
   - 应该显示Python版本号

### 方法2: 使用Microsoft Store

1. 打开Microsoft Store
2. 搜索 "Python"
3. 选择 "Python 3.11" 或更高版本
4. 点击 "安装"

### 方法3: 使用Chocolatey（如果已安装）

```cmd
choco install python
```

## 🚀 安装完成后启动应用

安装Python后，请按以下步骤启动应用：

### 1. 打开命令提示符
- 按 Win+R，输入 `cmd`，按回车

### 2. 进入项目目录
```cmd
cd "C:\Users\<USER>\Downloads\数字人女友app"
```

### 3. 运行启动脚本
```cmd
quick_start.bat
```

## 🔧 如果遇到问题

### Python命令不识别
- 确保安装时勾选了 "Add Python to PATH"
- 重启命令提示符
- 尝试使用 `python3` 或 `py` 命令

### 权限问题
- 以管理员身份运行命令提示符
- 右键点击命令提示符，选择"以管理员身份运行"

### 网络问题
- 如果下载慢，可以使用国内镜像
- 或者联系我获取离线安装包

## 📞 需要帮助？

如果安装过程中遇到任何问题，请：
1. 截图错误信息
2. 告诉我具体的错误提示
3. 我会帮您解决问题

安装完成后，我们就可以启动数字人女友应用了！ 💕
