"""
配置管理模块
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基本配置
    app_name: str = "数字人女友API"
    app_version: str = "1.0.0"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    database_url: str = "sqlite:///./digital_girlfriend.db"
    
    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI 服务配置
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-3.5-turbo"
    ai_temperature: float = 0.7
    ai_max_tokens: int = 150
    
    # 语音服务配置
    azure_speech_key: Optional[str] = None
    azure_speech_region: Optional[str] = None
    speech_language: str = "zh-CN"
    voice_name: str = "zh-CN-XiaoxiaoNeural"
    
    # 文件存储配置
    upload_dir: str = "uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_audio_formats: List[str] = ["wav", "mp3", "m4a", "ogg"]
    allowed_image_formats: List[str] = ["jpg", "jpeg", "png", "gif"]
    
    # Redis 配置
    redis_url: Optional[str] = None
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # CORS 配置
    allowed_origins: List[str] = ["*"]
    
    # WebSocket 配置
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 1000
    
    # 邮件配置
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    mail_from: Optional[str] = None
    
    # 监控配置
    enable_metrics: bool = False
    metrics_port: int = 9090

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保上传目录存在
        Path(self.upload_dir).mkdir(parents=True, exist_ok=True)
        # 确保日志目录存在
        Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)

# 创建全局配置实例
settings = Settings()

# 开发环境配置
class DevelopmentSettings(Settings):
    debug: bool = True
    log_level: str = "DEBUG"

# 生产环境配置
class ProductionSettings(Settings):
    debug: bool = False
    log_level: str = "WARNING"
    allowed_origins: List[str] = [
        "https://your-domain.com",
        "https://www.your-domain.com"
    ]

# 测试环境配置
class TestSettings(Settings):
    database_url: str = "sqlite:///./test.db"
    debug: bool = True
    log_level: str = "DEBUG"

def get_settings() -> Settings:
    """根据环境变量获取配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()

# 导出配置实例
settings = get_settings()
