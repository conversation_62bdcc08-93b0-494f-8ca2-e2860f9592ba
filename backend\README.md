# 数字人女友后端服务

基于 FastAPI 开发的AI数字人女友后端服务，提供智能对话、WebSocket实时通信、用户管理等功能。

## 功能特性

- 🤖 **AI智能对话** - 支持多种个性类型的AI回复
- 🔄 **WebSocket实时通信** - 实时消息推送
- 👤 **用户管理** - 用户注册、登录、资料管理
- 🎤 **语音处理** - 语音识别和合成
- 🖼️ **文件上传** - 支持音频、图片文件上传
- 📊 **数据统计** - 用户活动和聊天记录统计
- 🔒 **安全认证** - JWT token认证
- 📝 **API文档** - 自动生成的API文档

## 技术栈

- **FastAPI** - 现代高性能Web框架
- **SQLAlchemy** - ORM数据库操作
- **WebSocket** - 实时通信
- **OpenAI API** - AI对话生成（可选）
- **Azure Speech** - 语音识别和合成（可选）
- **SQLite/PostgreSQL** - 数据存储

## 快速开始

### 1. 环境要求

- Python 3.8+
- pip 包管理器

### 2. 安装

```bash
# 克隆项目
git clone <repository-url>
cd backend

# 运行安装脚本
python setup.py
```

### 3. 配置

编辑 `.env` 文件，配置您的设置：

```env
# 基本配置
DEBUG=True
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./digital_girlfriend.db

# AI服务配置（可选）
OPENAI_API_KEY=your-openai-api-key
AZURE_SPEECH_KEY=your-azure-speech-key
AZURE_SPEECH_REGION=your-azure-region
```

### 4. 运行服务

```bash
# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 启动服务
python run_server.py
```

### 5. 访问服务

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **WebSocket**: ws://localhost:8000/ws

## API 接口

### 聊天接口

#### 发送文本消息
```http
POST /api/chat/text
Content-Type: application/json

{
  "message": "你好",
  "timestamp": "2024-01-01T00:00:00Z",
  "user_id": "user123"
}
```

#### 发送语音消息
```http
POST /api/chat/audio
Content-Type: multipart/form-data

audio: <audio_file>
timestamp: "2024-01-01T00:00:00Z"
```

### WebSocket 通信

连接到 `ws://localhost:8000/ws` 进行实时通信：

```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

// 发送消息
ws.send(JSON.stringify({
  id: "msg123",
  content: "你好",
  type: "text",
  timestamp: new Date().toISOString(),
  isFromUser: true
}));

// 接收消息
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log('收到消息:', message);
};
```

## 项目结构

```
backend/
├── main.py              # FastAPI应用主文件
├── config.py            # 配置管理
├── database.py          # 数据库连接和会话管理
├── models.py            # 数据库模型
├── ai_service.py        # AI服务模块
├── run_server.py        # 服务启动脚本
├── setup.py             # 安装脚本
├── requirements.txt     # 依赖列表
├── .env.example         # 配置文件示例
└── README.md           # 说明文档
```

## 开发指南

### 添加新的API端点

1. 在 `main.py` 中添加路由函数
2. 定义请求/响应模型
3. 实现业务逻辑
4. 添加错误处理

### 扩展AI功能

1. 修改 `ai_service.py` 中的 `AIService` 类
2. 添加新的个性类型或回复逻辑
3. 集成外部AI服务

### 数据库迁移

```bash
# 创建迁移文件
alembic revision --autogenerate -m "描述"

# 执行迁移
alembic upgrade head
```

## 部署

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run_server.py"]
```

### 生产环境配置

1. 设置环境变量 `ENVIRONMENT=production`
2. 使用PostgreSQL数据库
3. 配置反向代理（Nginx）
4. 启用HTTPS
5. 设置日志轮转

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库URL配置
   - 确保数据库服务正在运行

2. **AI服务不可用**
   - 检查OpenAI API密钥
   - 验证网络连接

3. **WebSocket连接失败**
   - 检查防火墙设置
   - 验证端口是否被占用

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
