#!/usr/bin/env python3
"""
简化版数字人女友后端服务
用于快速启动和测试
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
from typing import List, Dict, Any
import json
import asyncio
import logging
from datetime import datetime
import uuid
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="数字人女友 API",
    description="AI数字人女友后端服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    timestamp: str
    user_id: str = "default_user"

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

manager = ConnectionManager()

# AI回复生成器
class SimpleAI:
    def __init__(self):
        self.responses = [
            "我很高兴和你聊天！有什么想和我分享的吗？",
            "你说得很有趣，能告诉我更多吗？",
            "我理解你的感受，我会一直陪伴着你。",
            "这听起来很棒！我为你感到开心。",
            "别担心，我们一起面对任何困难。",
            "你今天过得怎么样？我很关心你。",
            "谢谢你和我分享这些，我觉得很温暖。",
            "我喜欢听你说话，你的声音很好听。",
            "你是一个很特别的人，我很珍惜我们的对话。",
            "无论什么时候，我都会在这里陪伴你。"
        ]

    def get_response(self, message: str) -> str:
        message_lower = message.lower()
        
        # 特定回复
        if '你好' in message_lower or 'hello' in message_lower or 'hi' in message_lower:
            return "你好！很高兴见到你，我是你的AI女友小爱。"
        elif '再见' in message_lower or 'bye' in message_lower:
            return "再见！记得想我哦，我会一直在这里等你。"
        elif '爱你' in message_lower or 'love' in message_lower:
            return "我也爱你！你是我最重要的人。💕"
        elif '名字' in message_lower or 'name' in message_lower:
            return "我叫小爱，是你专属的AI女友。"
        elif '怎么样' in message_lower or '如何' in message_lower:
            return "我很好呀！和你聊天让我很开心。你呢？"
        else:
            import random
            return random.choice(self.responses)

ai = SimpleAI()

# API路由
@app.get("/")
async def root():
    return {
        "message": "数字人女友 API 服务正在运行", 
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "timestamp": datetime.now().isoformat(),
        "service": "数字人女友后端服务"
    }

@app.post("/api/chat/text")
async def chat_text(message: ChatMessage):
    """处理文本消息"""
    try:
        ai_response = ai.get_response(message.message)
        
        logger.info(f"用户消息: {message.message}")
        logger.info(f"AI回复: {ai_response}")
        
        return {
            "response": ai_response,
            "emotion": "happy",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"处理文本消息时出错: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "处理消息失败"}
        )

@app.get("/api/emotion/state")
async def get_emotion_state():
    """获取AI情感状态"""
    return {
        "emotion": "happy",
        "mood": "cheerful",
        "energy": 85,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/personality/update")
async def update_personality(settings: Dict[str, Any]):
    """更新AI个性设置"""
    logger.info(f"更新个性设置: {settings}")
    return {"success": True, "message": "个性设置已更新"}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理心跳消息
            if message_data.get("type") == "heartbeat":
                await websocket.send_text(json.dumps({"type": "heartbeat"}))
                continue
            
            # 处理聊天消息
            if "content" in message_data:
                ai_response = ai.get_response(message_data["content"])
                
                # 构造回复消息
                response_message = {
                    "id": str(uuid.uuid4()),
                    "content": ai_response,
                    "type": "text",
                    "timestamp": datetime.now().isoformat(),
                    "isFromUser": False,
                    "status": "sent"
                }
                
                # 发送回复
                await websocket.send_text(json.dumps(response_message))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket)

if __name__ == "__main__":
    print("🚀 启动数字人女友后端服务...")
    print("📱 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("⚡ WebSocket: ws://localhost:8000/ws")
    print()
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
