"""
数字人女友后端服务安装脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def create_virtual_environment():
    """创建虚拟环境"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✓ 虚拟环境已存在")
        return
    
    print("正在创建虚拟环境...")
    subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
    print("✓ 虚拟环境创建成功")

def get_pip_command():
    """获取pip命令"""
    if os.name == 'nt':  # Windows
        return str(Path("venv/Scripts/pip.exe"))
    else:  # Linux/Mac
        return str(Path("venv/bin/pip"))

def install_dependencies():
    """安装依赖"""
    pip_cmd = get_pip_command()
    
    print("正在升级pip...")
    subprocess.run([pip_cmd, "install", "--upgrade", "pip"], check=True)
    
    print("正在安装依赖包...")
    subprocess.run([pip_cmd, "install", "-r", "requirements.txt"], check=True)
    print("✓ 依赖安装完成")

def create_config_file():
    """创建配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✓ 配置文件已创建，请编辑 .env 文件设置您的配置")
    else:
        print("✓ 配置文件已存在")

def create_directories():
    """创建必要的目录"""
    directories = ["uploads", "uploads/audio", "uploads/images", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✓ 目录结构创建完成")

def main():
    """主安装函数"""
    print("=== 数字人女友后端服务安装程序 ===")
    print()
    
    try:
        # 检查Python版本
        check_python_version()
        
        # 创建虚拟环境
        create_virtual_environment()
        
        # 安装依赖
        install_dependencies()
        
        # 创建配置文件
        create_config_file()
        
        # 创建目录
        create_directories()
        
        print()
        print("=== 安装完成 ===")
        print()
        print("下一步操作:")
        print("1. 编辑 .env 文件，配置您的设置")
        print("2. 运行服务: python run_server.py")
        print("3. 访问 http://localhost:8000 查看API文档")
        print()
        
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"安装过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
