# 数字人女友应用运行指南

这是一个完整的AI数字人女友应用，包含Flutter前端和Python FastAPI后端。

## 📋 系统要求

### 前端 (Flutter)
- Flutter SDK 3.10.0+
- Dart SDK 3.0.0+
- Android Studio 或 VS Code
- Android SDK (Android开发)
- Xcode (iOS开发，仅macOS)

### 后端 (Python)
- Python 3.8+
- pip 包管理器

## 🚀 快速开始

### 1. 环境准备

#### 安装Flutter (如果未安装)
```bash
# 下载Flutter SDK
# 访问: https://flutter.dev/docs/get-started/install

# 配置环境变量
export PATH="$PATH:/path/to/flutter/bin"

# 验证安装
flutter doctor
```

#### 安装Python依赖
```bash
# 进入后端目录
cd backend

# 运行安装脚本
python setup.py

# 或手动安装
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 2. 配置后端

```bash
cd backend

# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

基本配置示例：
```env
DEBUG=True
HOST=0.0.0.0
PORT=8000
DATABASE_URL=sqlite:///./digital_girlfriend.db

# 可选：AI服务配置
OPENAI_API_KEY=your-openai-api-key
AZURE_SPEECH_KEY=your-azure-speech-key
```

### 3. 启动后端服务

```bash
cd backend

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 启动服务
python run_server.py
```

后端服务将在 http://localhost:8000 启动

### 4. 配置前端

```bash
# 回到项目根目录
cd ..

# 获取Flutter依赖
flutter pub get

# 检查Flutter环境
flutter doctor
```

### 5. 启动前端应用

```bash
# 运行在Chrome浏览器 (推荐用于开发)
flutter run -d chrome

# 或运行在Android设备/模拟器
flutter run

# 或运行在iOS设备/模拟器 (仅macOS)
flutter run -d ios
```

## 🧪 测试应用

### 运行集成测试
```bash
# 确保后端服务正在运行
python test_integration.py
```

### 运行Flutter测试
```bash
flutter test
```

### 手动测试步骤

1. **启动应用**
   - 打开应用，应该看到启动页面
   - 等待3秒后自动跳转到欢迎页面

2. **用户注册**
   - 输入昵称（必填）
   - 输入邮箱（可选）
   - 点击"开始聊天"

3. **聊天功能**
   - 在输入框输入消息
   - 点击发送按钮
   - 观察AI回复

4. **语音功能**
   - 长按麦克风按钮录音
   - 松开发送语音消息
   - 观察语音处理结果

5. **设置功能**
   - 点击右上角菜单
   - 进入设置页面
   - 测试各种设置选项

## 📱 功能特性

### 已实现功能
- ✅ 用户注册和登录
- ✅ 文本聊天
- ✅ 语音消息录制
- ✅ WebSocket实时通信
- ✅ AI个性化回复
- ✅ 用户设置管理
- ✅ 聊天记录查看
- ✅ 主题切换
- ✅ 离线模式支持

### 开发中功能
- 🔄 语音识别和合成
- 🔄 图片消息支持
- 🔄 视频通话功能
- 🔄 AI情感分析
- 🔄 个性化AI训练

## 🔧 故障排除

### 常见问题

1. **Flutter doctor 检查失败**
   ```bash
   # 安装Android许可证
   flutter doctor --android-licenses
   
   # 更新Flutter
   flutter upgrade
   ```

2. **后端服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :8000  # Linux/Mac
   netstat -ano | findstr :8000  # Windows
   
   # 检查Python版本
   python --version
   ```

3. **WebSocket连接失败**
   - 确保后端服务正在运行
   - 检查防火墙设置
   - 验证URL配置

4. **Flutter应用无法连接后端**
   - 检查网络权限配置
   - 验证API URL设置
   - 查看控制台错误信息

### 日志查看

```bash
# 后端日志
tail -f backend/logs/app.log

# Flutter日志
flutter logs
```

## 🏗️ 开发指南

### 添加新功能

1. **后端API**
   - 在 `backend/main.py` 添加路由
   - 更新数据模型 `backend/models.py`
   - 实现业务逻辑

2. **前端功能**
   - 创建新的Screen或Widget
   - 更新Provider状态管理
   - 添加路由配置

### 代码结构

```
数字人女友app/
├── lib/                    # Flutter前端代码
│   ├── main.dart          # 应用入口
│   ├── models/            # 数据模型
│   ├── providers/         # 状态管理
│   ├── screens/           # 页面UI
│   ├── services/          # 网络服务
│   ├── widgets/           # UI组件
│   └── utils/             # 工具类
├── backend/               # Python后端代码
│   ├── main.py           # FastAPI应用
│   ├── models.py         # 数据库模型
│   ├── ai_service.py     # AI服务
│   └── config.py         # 配置管理
├── test/                 # 测试文件
└── assets/               # 资源文件
```

## 🚀 部署指南

### 开发环境
- 前端：`flutter run -d chrome`
- 后端：`python run_server.py`

### 生产环境
- 前端：`flutter build web` 或 `flutter build apk`
- 后端：使用Docker或云服务部署

## 📞 获取帮助

- 查看项目README文件
- 检查GitHub Issues
- 运行测试脚本诊断问题

## 🎯 下一步

1. 完善AI对话功能
2. 添加更多个性化选项
3. 实现语音识别和合成
4. 优化用户体验
5. 准备应用发布

祝您使用愉快！ 💕
