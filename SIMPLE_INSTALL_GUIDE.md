# 🚀 数字人女友应用 - 简单安装指南

## 📋 当前状态
- ❌ Python 未安装
- ❌ Flutter 未安装

## 🎯 安装步骤

### 第1步：安装Python（必需）

#### 方法1：官网下载（推荐）
1. **打开浏览器**，访问：`https://www.python.org/downloads/`
2. **点击绿色按钮** "Download Python 3.11.x"
3. **下载完成后**，双击运行安装程序
4. **⚠️ 重要**：勾选 "Add Python to PATH" 复选框
5. **点击** "Install Now" 开始安装
6. **等待安装完成**

#### 验证Python安装
打开命令提示符（Win+R，输入cmd），输入：
```cmd
python --version
```
应该显示：`Python 3.11.x`

### 第2步：安装Flutter（可选，用于移动应用）

#### 下载Flutter SDK
1. **访问**：`https://docs.flutter.dev/get-started/install/windows`
2. **下载** Flutter SDK zip文件
3. **解压到** `C:\flutter` 目录
4. **添加环境变量**：
   - 按 Win+R，输入 `sysdm.cpl`
   - 点击"环境变量"
   - 在"系统变量"中找到"Path"，点击"编辑"
   - 添加：`C:\flutter\bin`
   - 点击"确定"保存

#### 验证Flutter安装
重新打开命令提示符，输入：
```cmd
flutter --version
```

## 🚀 安装完成后启动应用

### 方案1：仅启动后端（推荐）
```cmd
cd "C:\Users\<USER>\Downloads\数字人女友app\backend"
python simple_server.py
```
然后在浏览器访问：`http://localhost:8000/docs`

### 方案2：启动完整应用（需要Flutter）
```cmd
cd "C:\Users\<USER>\Downloads\数字人女友app"
flutter pub get
flutter run -d chrome
```

## 🎮 快速体验（无需安装）

如果暂时不想安装环境，您可以：
1. **双击** `demo.html` 文件
2. **在浏览器中** 立即体验AI对话功能

## 📞 需要帮助？

### 常见问题

**Q: Python安装后命令不识别？**
A: 确保安装时勾选了"Add Python to PATH"，重启命令提示符

**Q: 下载速度慢？**
A: 可以使用国内镜像或者联系我获取离线安装包

**Q: 权限问题？**
A: 以管理员身份运行命令提示符

### 联系支持
如果遇到任何问题：
1. 截图错误信息
2. 描述具体步骤
3. 我会立即帮您解决

## 🎯 推荐安装顺序

1. **先安装Python** - 这是必需的
2. **验证Python** - 确保能正常运行
3. **启动后端服务** - 测试基本功能
4. **（可选）安装Flutter** - 如果需要移动应用

让我们开始安装吧！ 💪
