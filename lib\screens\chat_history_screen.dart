import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../providers/chat_provider.dart';
import '../models/message.dart';
import '../widgets/message_bubble.dart';

class ChatHistoryScreen extends StatefulWidget {
  const ChatHistoryScreen({super.key});

  @override
  State<ChatHistoryScreen> createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  String _searchQuery = '';
  MessageType? _filterType;
  DateTime? _filterDate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('聊天记录'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'export':
                  _exportHistory();
                  break;
                case 'clear':
                  _showClearDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('导出记录'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('清空记录', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<ChatProvider>(
        builder: (context, chatProvider, child) {
          final messages = _getFilteredMessages(chatProvider.messages);
          
          if (messages.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 80.w,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    _searchQuery.isNotEmpty || _filterType != null || _filterDate != null
                        ? '没有找到匹配的消息'
                        : '暂无聊天记录',
                    style: TextStyle(
                      fontSize: 18.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (_searchQuery.isNotEmpty || _filterType != null || _filterDate != null) ...[
                    SizedBox(height: 16.h),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _searchQuery = '';
                          _filterType = null;
                          _filterDate = null;
                        });
                      },
                      child: const Text('清除筛选'),
                    ),
                  ],
                ],
              ),
            );
          }

          return Column(
            children: [
              // 筛选状态栏
              if (_searchQuery.isNotEmpty || _filterType != null || _filterDate != null)
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  color: Theme.of(context).colorScheme.primaryContainer,
                  child: Wrap(
                    spacing: 8.w,
                    children: [
                      if (_searchQuery.isNotEmpty)
                        Chip(
                          label: Text('搜索: $_searchQuery'),
                          onDeleted: () {
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                        ),
                      if (_filterType != null)
                        Chip(
                          label: Text('类型: ${_getTypeText(_filterType!)}'),
                          onDeleted: () {
                            setState(() {
                              _filterType = null;
                            });
                          },
                        ),
                      if (_filterDate != null)
                        Chip(
                          label: Text('日期: ${DateFormat('yyyy-MM-dd').format(_filterDate!)}'),
                          onDeleted: () {
                            setState(() {
                              _filterDate = null;
                            });
                          },
                        ),
                    ],
                  ),
                ),
              
              // 消息统计
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem('总消息', messages.length.toString()),
                    _buildStatItem('文本', messages.where((m) => m.type == MessageType.text).length.toString()),
                    _buildStatItem('语音', messages.where((m) => m.type == MessageType.audio).length.toString()),
                    _buildStatItem('图片', messages.where((m) => m.type == MessageType.image).length.toString()),
                  ],
                ),
              ),
              
              Divider(height: 1, color: Colors.grey[300]),
              
              // 消息列表
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.all(16.w),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    return Container(
                      margin: EdgeInsets.only(bottom: 8.h),
                      child: MessageBubble(
                        message: message,
                        onTap: () => _showMessageDetails(message),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  List<Message> _getFilteredMessages(List<Message> messages) {
    List<Message> filtered = List.from(messages);

    // 搜索筛选
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((message) {
        return message.content.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // 类型筛选
    if (_filterType != null) {
      filtered = filtered.where((message) => message.type == _filterType).toList();
    }

    // 日期筛选
    if (_filterDate != null) {
      filtered = filtered.where((message) {
        return DateFormat('yyyy-MM-dd').format(message.timestamp) == 
               DateFormat('yyyy-MM-dd').format(_filterDate!);
      }).toList();
    }

    return filtered;
  }

  String _getTypeText(MessageType type) {
    switch (type) {
      case MessageType.text:
        return '文本';
      case MessageType.audio:
        return '语音';
      case MessageType.image:
        return '图片';
      case MessageType.video:
        return '视频';
    }
  }

  void _showSearchDialog() {
    final TextEditingController controller = TextEditingController(text: _searchQuery);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索消息'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: '输入搜索关键词',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = controller.text.trim();
              });
              Navigator.pop(context);
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选消息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('消息类型:'),
            DropdownButton<MessageType?>(
              value: _filterType,
              isExpanded: true,
              items: [
                const DropdownMenuItem(value: null, child: Text('全部类型')),
                ...MessageType.values.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(_getTypeText(type)),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _filterType = value;
                });
              },
            ),
            SizedBox(height: 16.h),
            const Text('日期:'),
            ListTile(
              title: Text(_filterDate == null
                  ? '选择日期'
                  : DateFormat('yyyy-MM-dd').format(_filterDate!)),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _filterDate ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _filterDate = date;
                  });
                }
              },
            ),
            if (_filterDate != null)
              TextButton(
                onPressed: () {
                  setState(() {
                    _filterDate = null;
                  });
                },
                child: const Text('清除日期筛选'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showMessageDetails(Message message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('消息详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('内容', message.content),
            _buildDetailRow('类型', _getTypeText(message.type)),
            _buildDetailRow('发送者', message.isFromUser ? '我' : 'AI助手'),
            _buildDetailRow('时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(message.timestamp)),
            _buildDetailRow('状态', _getStatusText(message.status)),
            if (message.audioUrl != null)
              _buildDetailRow('音频文件', message.audioUrl!),
            if (message.imageUrl != null)
              _buildDetailRow('图片链接', message.imageUrl!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 12.sp),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return '发送中';
      case MessageStatus.sent:
        return '已发送';
      case MessageStatus.delivered:
        return '已送达';
      case MessageStatus.read:
        return '已读';
      case MessageStatus.failed:
        return '发送失败';
    }
  }

  void _exportHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出聊天记录'),
        content: const Text('此功能将导出您的聊天记录为文本文件。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 实现导出功能
              Get.snackbar('提示', '聊天记录导出功能开发中');
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  void _showClearDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空聊天记录'),
        content: const Text('确定要清空所有聊天记录吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final chatProvider = Provider.of<ChatProvider>(context, listen: false);
              chatProvider.clearMessages();
              Navigator.pop(context);
              Get.snackbar('成功', '聊天记录已清空');
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }
}
