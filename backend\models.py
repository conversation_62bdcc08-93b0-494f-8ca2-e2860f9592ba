"""
数据库模型定义
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=True)
    avatar = Column(String(255), nullable=True)
    password_hash = Column(String(255), nullable=True)  # 可选，用于真实认证
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 用户设置
    voice_enabled = Column(Boolean, default=True)
    notifications_enabled = Column(Boolean, default=True)
    language = Column(String(10), default="zh-CN")
    theme = Column(String(20), default="system")
    voice_speed = Column(Float, default=1.0)
    
    # 关联关系
    messages = relationship("Message", back_populates="user")
    chat_sessions = relationship("ChatSession", back_populates="user")

class ChatSession(Base):
    """聊天会话模型"""
    __tablename__ = "chat_sessions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    title = Column(String(200), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("Message", back_populates="chat_session")

class Message(Base):
    """消息模型"""
    __tablename__ = "messages"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    chat_session_id = Column(String, ForeignKey("chat_sessions.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default="text")  # text, audio, image, video
    is_from_user = Column(Boolean, nullable=False)
    status = Column(String(20), default="sent")  # sending, sent, delivered, read, failed
    audio_url = Column(String(500), nullable=True)
    image_url = Column(String(500), nullable=True)
    metadata = Column(Text, nullable=True)  # JSON格式的额外数据
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="messages")
    chat_session = relationship("ChatSession", back_populates="messages")

class AIPersonality(Base):
    """AI个性配置模型"""
    __tablename__ = "ai_personalities"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    name = Column(String(100), default="小爱")
    personality_type = Column(String(50), default="gentle")  # gentle, cheerful, mature, playful
    response_style = Column(String(50), default="caring")  # caring, humorous, intellectual, romantic
    interests = Column(Text, nullable=True)  # JSON格式的兴趣列表
    background_story = Column(Text, nullable=True)
    custom_responses = Column(Text, nullable=True)  # JSON格式的自定义回复
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class AudioFile(Base):
    """音频文件模型"""
    __tablename__ = "audio_files"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    message_id = Column(String, ForeignKey("messages.id"), nullable=True)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    duration = Column(Float, nullable=True)  # 音频时长（秒）
    format = Column(String(10), nullable=False)  # wav, mp3, m4a等
    transcription = Column(Text, nullable=True)  # 语音识别结果
    created_at = Column(DateTime, default=func.now())

class ImageFile(Base):
    """图片文件模型"""
    __tablename__ = "image_files"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    message_id = Column(String, ForeignKey("messages.id"), nullable=True)
    filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    format = Column(String(10), nullable=False)  # jpg, png, gif等
    created_at = Column(DateTime, default=func.now())

class UserActivity(Base):
    """用户活动记录模型"""
    __tablename__ = "user_activities"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    activity_type = Column(String(50), nullable=False)  # login, logout, send_message, etc.
    description = Column(String(500), nullable=True)
    metadata = Column(Text, nullable=True)  # JSON格式的额外数据
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=func.now())

class SystemLog(Base):
    """系统日志模型"""
    __tablename__ = "system_logs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    level = Column(String(20), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    module = Column(String(100), nullable=True)
    function = Column(String(100), nullable=True)
    line_number = Column(Integer, nullable=True)
    user_id = Column(String, nullable=True)
    request_id = Column(String, nullable=True)
    metadata = Column(Text, nullable=True)  # JSON格式的额外数据
    created_at = Column(DateTime, default=func.now())

# 数据库初始化函数
def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(bind=engine)

def drop_tables(engine):
    """删除所有表"""
    Base.metadata.drop_all(bind=engine)
