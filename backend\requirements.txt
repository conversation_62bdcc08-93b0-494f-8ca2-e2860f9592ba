# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# WebSocket 支持
websockets==12.0

# 数据验证和序列化
pydantic==2.5.0

# HTTP 客户端
httpx==0.25.2
requests==2.31.0

# 数据库支持
sqlalchemy==2.0.23
alembic==1.13.1
sqlite3

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 文件处理
aiofiles==23.2.0
pillow==10.1.0

# 音频处理
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1

# AI 和机器学习
openai==1.3.7
transformers==4.36.0
torch==2.1.1
numpy==1.24.3

# 语音识别和合成
speechrecognition==3.10.0
gtts==2.4.0
azure-cognitiveservices-speech==1.34.0

# 配置管理
python-dotenv==1.0.0
pydantic-settings==2.1.0

# 日志和监控
loguru==0.7.2

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 生产环境
gunicorn==21.2.0
