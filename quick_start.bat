@echo off
chcp 65001 >nul
echo ========================================
echo    数字人女友应用快速启动脚本
echo ========================================
echo.

:: 检查Python是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装，正在安装...
    echo.
    echo 请按照以下步骤手动安装Python:
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载Python 3.9或更高版本
    echo 3. 安装时勾选 "Add Python to PATH"
    echo 4. 安装完成后重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 检查是否在正确目录
if not exist "backend" (
    echo ❌ 未找到backend目录，请确保在正确的项目目录中运行
    pause
    exit /b 1
)

echo ✅ 项目目录检查通过

:: 进入后端目录
cd backend

:: 检查虚拟环境
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查requirements.txt
if not exist "requirements.txt" (
    echo ❌ 未找到requirements.txt文件
    pause
    exit /b 1
)

:: 安装依赖
echo 📦 安装Python依赖包...
pip install --upgrade pip
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试安装基础依赖...
    pip install fastapi uvicorn websockets pydantic
)

:: 检查配置文件
if not exist ".env" (
    if exist ".env.example" (
        echo 📝 创建配置文件...
        copy .env.example .env
        echo ✅ 配置文件已创建
    ) else (
        echo 📝 创建基础配置文件...
        echo DEBUG=True > .env
        echo HOST=0.0.0.0 >> .env
        echo PORT=8000 >> .env
        echo DATABASE_URL=sqlite:///./digital_girlfriend.db >> .env
    )
)

:: 创建必要目录
if not exist "uploads" mkdir uploads
if not exist "uploads\audio" mkdir uploads\audio
if not exist "uploads\images" mkdir uploads\images
if not exist "logs" mkdir logs

echo.
echo 🚀 启动后端服务...
echo 服务将在 http://localhost:8000 启动
echo 按 Ctrl+C 停止服务
echo.

:: 启动服务
python run_server.py

pause
