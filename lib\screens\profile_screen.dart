import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../providers/user_provider.dart';
import '../providers/chat_provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditDialog(context),
          ),
        ],
      ),
      body: Consumer<UserProvider>(
        builder: (context, userProvider, child) {
          final user = userProvider.currentUser;
          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(20.w),
            child: Column(
              children: [
                // 头像区域
                Center(
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 60.w,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        child: Icon(
                          Icons.person,
                          size: 60.w,
                          color: Colors.white,
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.secondary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.camera_alt, color: Colors.white),
                            onPressed: () {
                              // TODO: 更换头像功能
                              Get.snackbar('提示', '头像更换功能开发中');
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 24.h),

                // 用户信息卡片
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '基本信息',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 16.h),
                        _buildInfoRow('昵称', user.name),
                        if (user.email != null) 
                          _buildInfoRow('邮箱', user.email!),
                        _buildInfoRow('注册时间', _formatDate(user.createdAt)),
                        _buildInfoRow('用户ID', user.id),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.h),

                // 使用统计卡片
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '使用统计',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 16.h),
                        Consumer<ChatProvider>(
                          builder: (context, chatProvider, child) {
                            final messageCount = chatProvider.messages.length;
                            final userMessages = chatProvider.messages
                                .where((msg) => msg.isFromUser)
                                .length;
                            
                            return Column(
                              children: [
                                _buildStatRow('总消息数', messageCount.toString()),
                                _buildStatRow('发送消息', userMessages.toString()),
                                _buildStatRow('接收消息', (messageCount - userMessages).toString()),
                                _buildStatRow('使用天数', _calculateUsageDays(user.createdAt).toString()),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.h),

                // 偏好设置卡片
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '偏好设置',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 16.h),
                        _buildInfoRow('语音功能', user.settings.voiceEnabled ? '已启用' : '已关闭'),
                        _buildInfoRow('通知', user.settings.notificationsEnabled ? '已启用' : '已关闭'),
                        _buildInfoRow('主题', _getThemeText(user.settings.theme)),
                        _buildInfoRow('语言', _getLanguageText(user.settings.language)),
                        _buildInfoRow('语音速度', '${user.settings.voiceSpeed}x'),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 32.h),

                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _showEditDialog(context),
                        icon: const Icon(Icons.edit),
                        label: const Text('编辑资料'),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _exportData(context),
                        icon: const Icon(Icons.download),
                        label: const Text('导出数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),

                // 危险操作
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => _showDeleteAccountDialog(context),
                    icon: const Icon(Icons.delete_forever),
                    label: const Text('删除账户'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(height: 32.h),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  int _calculateUsageDays(DateTime createdAt) {
    return DateTime.now().difference(createdAt).inDays + 1;
  }

  String _getThemeText(String theme) {
    switch (theme) {
      case 'light':
        return '浅色';
      case 'dark':
        return '深色';
      case 'system':
      default:
        return '跟随系统';
    }
  }

  String _getLanguageText(String language) {
    switch (language) {
      case 'zh-CN':
        return '简体中文';
      case 'en-US':
        return 'English';
      default:
        return '简体中文';
    }
  }

  void _showEditDialog(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final user = userProvider.currentUser;
    if (user == null) return;

    final nameController = TextEditingController(text: user.name);
    final emailController = TextEditingController(text: user.email ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑资料'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '昵称',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: '邮箱',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await userProvider.updateUser(
                  name: nameController.text.trim(),
                  email: emailController.text.trim().isEmpty
                      ? null
                      : emailController.text.trim(),
                );
                Navigator.pop(context);
                Get.snackbar('成功', '资料更新成功');
              } catch (e) {
                Get.snackbar('错误', '更新失败: $e');
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出数据'),
        content: const Text('此功能将导出您的聊天记录和个人设置。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 实现数据导出功能
              Get.snackbar('提示', '数据导出功能开发中');
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除账户'),
        content: const Text('警告：此操作将永久删除您的账户和所有数据，且无法恢复。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmDeleteAccount(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteAccount(BuildContext context) {
    final TextEditingController confirmController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('请输入 "DELETE" 来确认删除账户：'),
            SizedBox(height: 16.h),
            TextField(
              controller: confirmController,
              decoration: const InputDecoration(
                hintText: '输入 DELETE',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              if (confirmController.text.trim() == 'DELETE') {
                try {
                  final userProvider = Provider.of<UserProvider>(context, listen: false);
                  final chatProvider = Provider.of<ChatProvider>(context, listen: false);

                  // 清空聊天记录
                  chatProvider.clearMessages();

                  // 删除用户数据
                  await userProvider.logout();

                  Navigator.pop(context);
                  Get.offAllNamed('/');
                  Get.snackbar('成功', '账户已删除');
                } catch (e) {
                  Get.snackbar('错误', '删除账户失败: $e');
                }
              } else {
                Get.snackbar('错误', '请正确输入 DELETE');
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
  }
}
