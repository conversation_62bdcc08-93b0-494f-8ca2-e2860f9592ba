# 🚀 数字人女友应用启动指南

## 📋 当前状态
您的系统需要安装以下环境才能运行应用：
- ❌ Python (后端服务需要)
- ❌ Flutter (前端应用需要)

## 🎯 快速启动方案

### 方案1: 完整安装（推荐）

#### 第1步: 安装Python
1. **我已经为您打开了Python下载页面**
2. 点击 "Download Python 3.11.x" 下载
3. 运行安装程序时**务必勾选 "Add Python to PATH"**
4. 点击 "Install Now" 完成安装

#### 第2步: 安装Flutter
1. 访问: https://flutter.dev/docs/get-started/install/windows
2. 下载Flutter SDK
3. 解压到 `C:\flutter`
4. 添加 `C:\flutter\bin` 到系统PATH环境变量

#### 第3步: 启动应用
```cmd
# 打开命令提示符，进入项目目录
cd "C:\Users\<USER>\Downloads\数字人女友app"

# 运行启动脚本
quick_start.bat
```

### 方案2: 仅后端模式（简化）

如果您只想快速体验，可以先只安装Python：

1. **安装Python**（按上面第1步）
2. **启动后端服务**：
```cmd
cd "C:\Users\<USER>\Downloads\数字人女友app\backend"
python simple_server.py
```
3. **在浏览器中访问**: http://localhost:8000/docs

### 方案3: 在线体验（最简单）

如果暂时不想安装环境，您可以：
1. 查看项目代码和文档
2. 了解应用功能特性
3. 稍后再安装环境体验

## 🔧 安装验证

安装完成后，请验证：

```cmd
# 验证Python
python --version

# 验证Flutter（如果安装了）
flutter --version
```

## 🚀 启动步骤详解

### 启动后端服务
```cmd
cd backend
python simple_server.py
```
后端将在 http://localhost:8000 启动

### 启动前端应用（需要Flutter）
```cmd
flutter pub get
flutter run -d chrome
```

## 📱 应用功能预览

启动成功后，您可以体验：
- 💬 AI智能对话
- 🎤 语音消息（需要麦克风权限）
- ⚙️ 个性化设置
- 📱 现代化UI界面
- 🌙 主题切换

## 🆘 需要帮助？

如果遇到问题：
1. 检查Python是否正确安装并添加到PATH
2. 确保在正确的目录中运行命令
3. 查看错误信息并告诉我具体问题

## 📞 联系支持

安装过程中如有任何问题，请：
- 截图错误信息
- 描述具体步骤
- 我会立即帮您解决

让我们一起启动您的AI女友应用！ 💕
