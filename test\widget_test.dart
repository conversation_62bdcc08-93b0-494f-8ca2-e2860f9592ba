import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:digital_girlfriend_app/main.dart';
import 'package:digital_girlfriend_app/providers/chat_provider.dart';
import 'package:digital_girlfriend_app/providers/user_provider.dart';
import 'package:digital_girlfriend_app/models/message.dart';
import 'package:digital_girlfriend_app/models/user.dart';

void main() {
  group('数字人女友应用测试', () {
    testWidgets('应用启动测试', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(const MyApp());

      // 验证启动页面存在
      expect(find.text('数字人女友'), findsOneWidget);
      expect(find.text('AI陪伴，温暖如初'), findsOneWidget);
    });

    testWidgets('欢迎页面测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ChatProvider()),
            ChangeNotifierProvider(create: (_) => UserProvider()),
          ],
          child: MaterialApp(
            home: const WelcomeScreen(),
          ),
        ),
      );

      // 验证欢迎页面元素
      expect(find.text('欢迎来到数字人女友'), findsOneWidget);
      expect(find.text('您的昵称'), findsOneWidget);
      expect(find.text('开始聊天'), findsOneWidget);
    });

    testWidgets('聊天界面测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => ChatProvider()),
            ChangeNotifierProvider(create: (_) => UserProvider()),
          ],
          child: MaterialApp(
            home: const ChatScreen(),
          ),
        ),
      );

      // 验证聊天界面元素
      expect(find.text('小爱'), findsOneWidget);
      expect(find.text('开始和小爱聊天吧！'), findsOneWidget);
      expect(find.text('输入消息...'), findsOneWidget);
    });
  });

  group('消息模型测试', () {
    test('消息创建测试', () {
      final message = Message(
        id: 'test_id',
        content: '测试消息',
        type: MessageType.text,
        timestamp: DateTime.now(),
        isFromUser: true,
      );

      expect(message.id, 'test_id');
      expect(message.content, '测试消息');
      expect(message.type, MessageType.text);
      expect(message.isFromUser, true);
      expect(message.status, MessageStatus.sent);
    });

    test('消息JSON序列化测试', () {
      final message = Message(
        id: 'test_id',
        content: '测试消息',
        type: MessageType.text,
        timestamp: DateTime(2024, 1, 1),
        isFromUser: true,
      );

      final json = message.toJson();
      expect(json['id'], 'test_id');
      expect(json['content'], '测试消息');
      expect(json['type'], 'text');
      expect(json['isFromUser'], true);

      final fromJson = Message.fromJson(json);
      expect(fromJson.id, message.id);
      expect(fromJson.content, message.content);
      expect(fromJson.type, message.type);
      expect(fromJson.isFromUser, message.isFromUser);
    });
  });

  group('用户模型测试', () {
    test('用户创建测试', () {
      final user = User(
        id: 'test_user',
        name: '测试用户',
        avatar: 'default',
        createdAt: DateTime.now(),
        settings: UserSettings(),
      );

      expect(user.id, 'test_user');
      expect(user.name, '测试用户');
      expect(user.avatar, 'default');
      expect(user.settings.voiceEnabled, true);
      expect(user.settings.language, 'zh-CN');
    });

    test('用户设置测试', () {
      final settings = UserSettings(
        voiceEnabled: false,
        theme: 'dark',
        voiceSpeed: 1.5,
      );

      expect(settings.voiceEnabled, false);
      expect(settings.theme, 'dark');
      expect(settings.voiceSpeed, 1.5);

      final updated = settings.copyWith(voiceEnabled: true);
      expect(updated.voiceEnabled, true);
      expect(updated.theme, 'dark'); // 保持不变
    });
  });

  group('Provider测试', () {
    test('ChatProvider消息管理测试', () {
      final chatProvider = ChatProvider();
      
      expect(chatProvider.messages.length, 0);
      expect(chatProvider.isLoading, false);
      
      // 注意：这里只测试初始状态，实际的消息发送需要mock网络请求
    });

    test('UserProvider用户管理测试', () {
      final userProvider = UserProvider();
      
      expect(userProvider.currentUser, null);
      expect(userProvider.isLoggedIn, false);
      expect(userProvider.isLoading, false);
    });
  });

  group('UI组件测试', () {
    testWidgets('MessageBubble组件测试', (WidgetTester tester) async {
      final message = Message(
        id: 'test_id',
        content: '测试消息内容',
        type: MessageType.text,
        timestamp: DateTime.now(),
        isFromUser: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MessageBubble(message: message),
          ),
        ),
      );

      expect(find.text('测试消息内容'), findsOneWidget);
    });

    testWidgets('ChatInput组件测试', (WidgetTester tester) async {
      bool messageSent = false;
      String sentMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChatInput(
              onSendMessage: (message) {
                messageSent = true;
                sentMessage = message;
              },
              onSendAudio: (audioPath) {},
            ),
          ),
        ),
      );

      // 查找输入框
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // 输入文本
      await tester.enterText(textField, '测试消息');
      await tester.pump();

      // 查找发送按钮并点击
      final sendButton = find.byIcon(Icons.send);
      expect(sendButton, findsOneWidget);
      
      await tester.tap(sendButton);
      await tester.pump();

      // 验证消息发送
      expect(messageSent, true);
      expect(sentMessage, '测试消息');
    });
  });

  group('网络服务测试', () {
    test('AI服务URL配置测试', () {
      // 这里可以测试AI服务的配置是否正确
      // 实际的网络请求测试需要mock
      expect(AIService.baseUrl, isNotNull);
    });

    test('WebSocket服务URL配置测试', () {
      // 测试WebSocket服务配置
      expect(WebSocketService.wsUrl, isNotNull);
      expect(WebSocketService.wsUrl, contains('ws://'));
    });
  });

  group('主题和样式测试', () {
    test('应用主题配置测试', () {
      final lightTheme = AppTheme.lightTheme;
      final darkTheme = AppTheme.darkTheme;

      expect(lightTheme.brightness, Brightness.light);
      expect(darkTheme.brightness, Brightness.dark);
      
      // 验证主色调
      expect(lightTheme.colorScheme.primary, AppTheme.primaryColor);
      expect(darkTheme.colorScheme.primary, AppTheme.primaryColor);
    });
  });

  group('错误处理测试', () {
    test('消息发送失败处理', () {
      // 测试网络错误时的处理逻辑
      // 这里需要mock网络请求失败的情况
    });

    test('WebSocket连接失败处理', () {
      // 测试WebSocket连接失败时的处理
    });
  });

  group('数据持久化测试', () {
    test('用户数据存储测试', () {
      // 测试SharedPreferences存储
      // 需要mock SharedPreferences
    });

    test('聊天记录存储测试', () {
      // 测试聊天记录的本地存储
    });
  });
}
