"""
数据库连接和会话管理
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator
import logging

from config import settings
from models import Base, create_tables

logger = logging.getLogger(__name__)

# 创建数据库引擎
if settings.database_url.startswith("sqlite"):
    # SQLite 配置
    engine = create_engine(
        settings.database_url,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=settings.debug
    )
else:
    # PostgreSQL/MySQL 配置
    engine = create_engine(
        settings.database_url,
        pool_pre_ping=True,
        echo=settings.debug
    )

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    """初始化数据库"""
    try:
        # 创建所有表
        create_tables(engine)
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@contextmanager
def get_db_context():
    """数据库会话上下文管理器"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"数据库操作失败: {e}")
        raise
    finally:
        db.close()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def create_session(self) -> Session:
        """创建新的数据库会话"""
        return self.SessionLocal()
    
    def init_database(self):
        """初始化数据库"""
        init_db()
    
    def reset_database(self):
        """重置数据库（删除所有表并重新创建）"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库重置成功")
        except Exception as e:
            logger.error(f"数据库重置失败: {e}")
            raise
    
    def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False

# 创建全局数据库管理器实例
db_manager = DatabaseManager()

# 数据库依赖注入
def get_database_session():
    """FastAPI 依赖注入：获取数据库会话"""
    return next(get_db())

# 初始化数据库（在应用启动时调用）
def startup_database():
    """应用启动时初始化数据库"""
    try:
        logger.info("正在初始化数据库...")
        db_manager.init_database()
        
        # 检查连接
        if db_manager.check_connection():
            logger.info("数据库连接正常")
        else:
            logger.error("数据库连接失败")
            
    except Exception as e:
        logger.error(f"数据库启动失败: {e}")
        raise

def shutdown_database():
    """应用关闭时清理数据库连接"""
    try:
        engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"数据库关闭失败: {e}")

# 数据库健康检查
async def health_check_database() -> dict:
    """数据库健康检查"""
    try:
        is_connected = db_manager.check_connection()
        return {
            "database": "healthy" if is_connected else "unhealthy",
            "connection": "ok" if is_connected else "failed",
            "url": settings.database_url.split("://")[0] + "://***"  # 隐藏敏感信息
        }
    except Exception as e:
        return {
            "database": "unhealthy",
            "connection": "failed",
            "error": str(e)
        }
