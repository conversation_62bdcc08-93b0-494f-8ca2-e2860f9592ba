@echo off
chcp 65001 >nul
echo ========================================
echo    数字人女友应用环境安装助手
echo ========================================
echo.

echo 📋 安装检查清单:
echo [ ] Python 3.11+
echo [ ] Flutter SDK
echo [ ] 环境变量配置
echo.

echo 🔍 正在检查当前环境...
echo.

:: 检查Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python已安装:
    python --version
    set PYTHON_INSTALLED=1
) else (
    echo ❌ Python未安装
    set PYTHON_INSTALLED=0
)

:: 检查Flutter
flutter --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter已安装:
    flutter --version | findstr "Flutter"
    set FLUTTER_INSTALLED=1
) else (
    echo ❌ Flutter未安装
    set FLUTTER_INSTALLED=0
)

echo.
echo ========================================

if %PYTHON_INSTALLED% equ 0 (
    echo 📥 需要安装Python
    echo.
    echo 请按照以下步骤安装Python:
    echo 1. 我将为您打开Python下载页面
    echo 2. 点击 "Download Python 3.11.x" 按钮
    echo 3. 下载完成后运行安装程序
    echo 4. ⚠️  重要: 勾选 "Add Python to PATH" 选项
    echo 5. 点击 "Install Now" 完成安装
    echo.
    echo 按任意键打开下载页面...
    pause >nul
    start https://www.python.org/downloads/
    echo.
    echo 安装完成后请重新运行此脚本验证安装
    pause
    exit /b 0
)

if %FLUTTER_INSTALLED% equ 0 (
    echo 📥 需要安装Flutter
    echo.
    echo 请按照以下步骤安装Flutter:
    echo 1. 我将为您打开Flutter安装页面
    echo 2. 下载Flutter SDK zip文件
    echo 3. 解压到 C:\flutter 目录
    echo 4. 添加 C:\flutter\bin 到系统PATH
    echo.
    echo 按任意键打开下载页面...
    pause >nul
    start https://docs.flutter.dev/get-started/install/windows
    echo.
    echo 安装完成后请重新运行此脚本验证安装
    pause
    exit /b 0
)

echo 🎉 环境检查完成！
echo.
echo ✅ Python: 已安装
echo ✅ Flutter: 已安装
echo.
echo 🚀 现在可以启动应用了！
echo.
echo 选择启动方式:
echo 1. 启动后端服务 (推荐先启动)
echo 2. 启动Flutter应用
echo 3. 同时启动前后端
echo 4. 退出
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" goto start_backend
if "%choice%"=="2" goto start_flutter
if "%choice%"=="3" goto start_both
if "%choice%"=="4" goto end

:start_backend
echo.
echo 🔧 启动后端服务...
cd backend
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
) else (
    echo 📦 创建虚拟环境...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo 📦 安装依赖...
    pip install fastapi uvicorn websockets pydantic
)
echo.
echo 🚀 启动服务器...
python simple_server.py
goto end

:start_flutter
echo.
echo 📱 启动Flutter应用...
flutter pub get
flutter run -d chrome
goto end

:start_both
echo.
echo 🚀 同时启动前后端...
echo 正在启动后端服务...
start cmd /k "cd backend && python simple_server.py"
timeout /t 3 >nul
echo 正在启动Flutter应用...
flutter pub get
flutter run -d chrome
goto end

:end
echo.
echo 感谢使用数字人女友应用！
pause
