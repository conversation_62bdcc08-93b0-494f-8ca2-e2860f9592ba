version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: digital_girlfriend_backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=**************************************/digital_girlfriend
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: digital_girlfriend_db
    environment:
      - POSTGRES_DB=digital_girlfriend
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: digital_girlfriend_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: digital_girlfriend_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./web:/usr/share/nginx/html
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - app-network

  # 前端Web应用 (可选，如果部署Web版本)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: digital_girlfriend_frontend
    volumes:
      - ./web:/app/build/web
    depends_on:
      - backend
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
