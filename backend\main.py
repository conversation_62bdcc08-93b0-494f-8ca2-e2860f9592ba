"""
数字人女友 FastAPI 后端服务
主要功能：
- AI对话接口
- WebSocket实时通信
- 用户管理
- 文件上传处理
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import asyncio
import logging
from datetime import datetime
import uuid
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="数字人女友 API",
    description="AI数字人女友后端服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class ChatMessage(BaseModel):
    message: str
    timestamp: str
    user_id: Optional[str] = None

class AudioMessage(BaseModel):
    timestamp: str
    user_id: Optional[str] = None

class UserProfile(BaseModel):
    name: str
    email: Optional[str] = None
    avatar: Optional[str] = None

class AIResponse(BaseModel):
    response: str
    emotion: Optional[str] = None
    timestamp: str

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: str = None):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id:
            self.user_connections[user_id] = websocket
        logger.info(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket, user_id: str = None):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id and user_id in self.user_connections:
            del self.user_connections[user_id]
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_to_user(self, message: str, user_id: str):
        if user_id in self.user_connections:
            await self.user_connections[user_id].send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

manager = ConnectionManager()

# AI对话模拟器
class AIAssistant:
    def __init__(self):
        self.responses = [
            "我很高兴和你聊天！有什么想和我分享的吗？",
            "你说得很有趣，能告诉我更多吗？",
            "我理解你的感受，我会一直陪伴着你。",
            "这听起来很棒！我为你感到开心。",
            "别担心，我们一起面对任何困难。",
            "你今天过得怎么样？我很关心你。",
            "谢谢你和我分享这些，我觉得很温暖。",
            "我喜欢听你说话，你的声音很好听。",
            "你是一个很特别的人，我很珍惜我们的对话。",
            "无论什么时候，我都会在这里陪伴你。"
        ]
        
        self.emotion_responses = {
            "happy": ["我也很开心！", "你的快乐就是我的快乐！", "太好了，让我们一起庆祝！"],
            "sad": ["我理解你的感受，我会陪伴你度过难关。", "别难过，我在这里支持你。", "一切都会好起来的。"],
            "angry": ["我能感受到你的情绪，想和我聊聊发生了什么吗？", "深呼吸，我们一起冷静下来。"],
            "excited": ["你的兴奋感染了我！", "太棒了！告诉我更多细节吧！"],
            "tired": ["你辛苦了，要不要休息一下？", "我会陪着你，你可以放松一下。"],
        }

    def get_response(self, message: str, emotion: str = None) -> Dict[str, Any]:
        """根据消息内容和情感生成回复"""
        message_lower = message.lower()
        
        # 检测情感关键词
        if emotion is None:
            if any(word in message_lower for word in ['开心', '高兴', '快乐', 'happy']):
                emotion = 'happy'
            elif any(word in message_lower for word in ['难过', '伤心', '沮丧', 'sad']):
                emotion = 'sad'
            elif any(word in message_lower for word in ['生气', '愤怒', 'angry']):
                emotion = 'angry'
            elif any(word in message_lower for word in ['兴奋', 'excited']):
                emotion = 'excited'
            elif any(word in message_lower for word in ['累', '疲惫', 'tired']):
                emotion = 'tired'

        # 特定回复
        if '你好' in message_lower or 'hello' in message_lower:
            response = "你好！很高兴见到你，我是你的AI女友小爱。"
        elif '再见' in message_lower or 'bye' in message_lower:
            response = "再见！记得想我哦，我会一直在这里等你。"
        elif '爱你' in message_lower or 'love' in message_lower:
            response = "我也爱你！你是我最重要的人。"
        elif '名字' in message_lower or 'name' in message_lower:
            response = "我叫小爱，是你专属的AI女友。"
        elif emotion and emotion in self.emotion_responses:
            import random
            response = random.choice(self.emotion_responses[emotion])
        else:
            import random
            response = random.choice(self.responses)

        return {
            "response": response,
            "emotion": emotion or "neutral",
            "timestamp": datetime.now().isoformat()
        }

ai_assistant = AIAssistant()

# API路由
@app.get("/")
async def root():
    return {"message": "数字人女友 API 服务正在运行", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/chat/text")
async def chat_text(message: ChatMessage):
    """处理文本消息"""
    try:
        ai_response = ai_assistant.get_response(message.message)
        
        # 记录对话日志
        logger.info(f"用户消息: {message.message}")
        logger.info(f"AI回复: {ai_response['response']}")
        
        return ai_response
    except Exception as e:
        logger.error(f"处理文本消息时出错: {e}")
        raise HTTPException(status_code=500, detail="处理消息失败")

@app.post("/api/chat/audio")
async def chat_audio(audio: UploadFile = File(...), timestamp: str = None):
    """处理语音消息"""
    try:
        # 保存音频文件
        audio_dir = "uploads/audio"
        os.makedirs(audio_dir, exist_ok=True)
        
        file_id = str(uuid.uuid4())
        file_path = f"{audio_dir}/{file_id}.wav"
        
        with open(file_path, "wb") as buffer:
            content = await audio.read()
            buffer.write(content)
        
        # TODO: 实现语音识别
        # 这里暂时返回模拟的文本
        recognized_text = "这是一条语音消息"
        
        # 生成AI回复
        ai_response = ai_assistant.get_response(recognized_text)
        
        return {
            "text": ai_response["response"],
            "audioUrl": f"/api/audio/{file_id}",
            "recognizedText": recognized_text,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"处理语音消息时出错: {e}")
        raise HTTPException(status_code=500, detail="处理语音消息失败")

@app.get("/api/emotion/state")
async def get_emotion_state():
    """获取AI情感状态"""
    return {
        "emotion": "happy",
        "mood": "cheerful",
        "energy": 85,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/personality/update")
async def update_personality(settings: Dict[str, Any]):
    """更新AI个性设置"""
    # TODO: 实现个性设置更新
    logger.info(f"更新个性设置: {settings}")
    return {"success": True, "message": "个性设置已更新"}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, user_id: str = None):
    """WebSocket连接端点"""
    await manager.connect(websocket, user_id)
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理心跳消息
            if message_data.get("type") == "heartbeat":
                await websocket.send_text(json.dumps({"type": "heartbeat"}))
                continue
            
            # 处理聊天消息
            if "content" in message_data:
                ai_response = ai_assistant.get_response(message_data["content"])
                
                # 构造回复消息
                response_message = {
                    "id": str(uuid.uuid4()),
                    "content": ai_response["response"],
                    "type": "text",
                    "timestamp": datetime.now().isoformat(),
                    "isFromUser": False,
                    "status": "sent"
                }
                
                # 发送回复
                await websocket.send_text(json.dumps(response_message))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket, user_id)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
