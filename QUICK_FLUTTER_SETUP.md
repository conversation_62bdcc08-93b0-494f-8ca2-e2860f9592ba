# 快速Flutter环境设置指南

## 🚀 最简单的安装方法

### 1. 下载Flutter SDK
1. 访问: https://flutter.dev/docs/get-started/install/windows
2. 下载Flutter SDK zip文件
3. 解压到 `C:\flutter` 目录

### 2. 配置环境变量
1. 按 `Win + R`，输入 `sysdm.cpl`
2. 点击"环境变量"
3. 在"系统变量"中找到"Path"，点击"编辑"
4. 添加: `C:\flutter\bin`
5. 点击"确定"保存

### 3. 验证安装
重新打开命令提示符或PowerShell：
```bash
flutter --version
flutter doctor
```

### 4. 安装Android Studio (可选)
1. 下载: https://developer.android.com/studio
2. 安装并启动
3. 安装Flutter插件

## 🔧 项目运行步骤

1. 打开命令提示符，进入项目目录：
```bash
cd "C:\Users\<USER>\Downloads\数字人女友app"
```

2. 获取依赖：
```bash
flutter pub get
```

3. 运行项目：
```bash
flutter run
```

## 📱 如果没有Android Studio

可以使用Chrome浏览器运行：
```bash
flutter run -d chrome
```

## ⚠️ 常见问题

1. **'flutter' 不是内部或外部命令**
   - 检查PATH环境变量是否正确配置
   - 重新启动命令提示符

2. **Android licenses not accepted**
   ```bash
   flutter doctor --android-licenses
   ```
   然后按'y'接受所有许可证

3. **无法连接设备**
   - 使用Chrome浏览器: `flutter run -d chrome`
   - 或使用Android模拟器

## 🎯 项目特定配置

本项目需要后端服务支持，如果后端未启动，应用会显示连接错误，这是正常的。
